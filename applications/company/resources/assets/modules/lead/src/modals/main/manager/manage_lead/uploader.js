'use strict';

const XHRUpload = require("@uppy/xhr-upload");
const MAX_FILES = 5;
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10 MB

const Api = require('@ca-package/api');
const Uppy = require("@uppy/core");
const Dashboard = require("@uppy/dashboard");

module.exports = {
    initUppy(edit_mode, current_files = []) {
        const max_number_of_files = MAX_FILES - current_files.length;
        return new Uppy({
            id: 'manage-lead-form-uploader',
            restrictions: {
                allowedFileTypes: ['image/jpeg','image/png','application/pdf'],
                maxNumberOfFiles: max_number_of_files,
                maxFileSize: MAX_FILE_SIZE
            }
        })
            .use(Dashboard, {
                inline: false,
                closeAfterFinish: true,
                showProgressDetails: true,
                proudlyDisplayPoweredByUppy: false,
                note: `Images and PDFs only (up to ${max_number_of_files} files)`,
                height: 150,
                metaFields: [
                    {id: 'name', name: 'Name', placeholder: 'File name'}
                ],
            });
    },
    addXHRUpload(uppy) {
        uppy.use(XHRUpload, {
            endpoint: Api.Resources.LeadFiles().buildUrl(),
            method: 'POST',
            headers: { Accept: 'application/vnd.adg.fx.collection-v1+json' },
            fieldName: 'file',
            metaFields: ['lead_id', 'name']
        });
    },
    updateUppyRestrictions(uppy, current_files = []) {
        const allowed = MAX_FILES - (current_files.length);
        uppy.setOptions({
            restrictions: { maxNumberOfFiles: allowed },
        });
        const has_dashboard = !!uppy.getPlugin('Dashboard');
            if (has_dashboard) {
                uppy.getPlugin('Dashboard').setOptions({
                    note:`Images and PDFs only (up to ${allowed} files)`
                });
            }
    },
    async manualUppyUpload(uppy, lead_id) {
        if (!uppy) return;

        Object.values(uppy.getState().files).forEach(f => {
            uppy.setFileMeta(f.id, {lead_id: parseInt(lead_id)});
        });

        uppy.setOptions({ autoProceed: true });
        if (Object.keys(uppy.getState().files).length === 0) return;
        await uppy.upload();
    },
}