<?php

use App\Services\Quickbooks\Resources\InvoiceResource;
use App\Services\QuickbooksService;
use Core\Components\Http\Exceptions\HttpResponseException;
use Core\Components\Http\StaticAccessors\Response;

if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];

		if ($primary == 1 || $projectManagement == 1 || $sales == 1) {

			if(isset($_GET['invoice'])) {
				$invoiceNumber = filter_input(INPUT_GET, 'invoice', FILTER_SANITIZE_NUMBER_INT);
			}
			if(isset($_GET['id'])) {
				$invoiceID = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_NUMBER_INT);
			}
			if(isset($_GET['project'])) {
				$projectID = filter_input(INPUT_GET, 'project', FILTER_SANITIZE_NUMBER_INT);
			}
			if(isset($_GET['name'])) {
				$invoiceName = filter_input(INPUT_GET, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
				$invoiceName = str_replace(' ', '', $invoiceName);
			}

			include_once(__DIR__ . '/includes/classes/class_Project.php');
			
			$object = new Project();
			$object->setProject($projectID, $companyID);
			$object->getProject();
			$projectArray = $object->getResults();	

			if (!empty($projectArray)) {

				//Project
				$firstName = $projectArray['firstName'];
				$lastName = $projectArray['lastName'];
				$quickbooksID = $projectArray['quickbooksID'];

				$quickbooks_service = new QuickbooksService((int) $companyID);
				$invoice_resource = new InvoiceResource($quickbooks_service);

				if (empty($invoiceID)) {

				    try {
                        $invoice = $invoice_resource->query()->where('DocNumber', $invoiceNumber)
                            ->where('CustomerRef', $quickbooksID)
                            ->limit(1)
                            ->first();
                        if ($invoice === null) {
                            throw new HttpResponseException(404);
                        }
                        $invoiceID = $invoice->Id;
                    } catch (\Throwable $e) {
                        throw new HttpResponseException(500);
                    }
				}

				if ($invoiceID) {
					$path = $invoice_resource->downloadPdf($invoiceID);
					return Response::file($path)
                        ->contentType('application/pdf')
                        ->download("{$firstName}-{$lastName}-{$invoiceName}-Invoice.pdf");
				}

			}
			
		}
	
?>