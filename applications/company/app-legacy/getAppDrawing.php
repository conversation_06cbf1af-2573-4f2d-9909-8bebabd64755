<?php

use App\Classes\Acl;
use App\Classes\FX\AppDrawing;
use App\Classes\Log;
use App\Resources\DrawingResource;
use Common\Models\EvaluationNewDrawing;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Exceptions\AppException;
use mikehaertl\pdftk\Pdf;
use Ramsey\Uuid\Uuid;

include_once(__DIR__ . '/includes/classes/class_Evaluation.php');

    $user = Auth::user();
    $userID = $user->userID;
    $companyID = $user->companyID;

if (isset($_GET['evaluationID'])) {
    $evaluationID = filter_input(INPUT_GET, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
}

$object = new Evaluation();
$object->setEvaluation($companyID, $evaluationID);
$object->getEvaluation();

if (($evaluation = $object->getResults()) === null) {
    throw new Exception('Unable to find evaluation');
}

// repair plan pro drawings
$evaluationDrawings = EvaluationNewDrawing::where('evaluationID', $evaluationID)->get();

// repair plan drawings
$appDrawing = new AppDrawing();
$appDrawing->getAllDrawings($evaluationID);

if (count($evaluationDrawings) > 0 || !empty($appDrawing->UUIDS)) {
    // create temporary bid file
    $pdf = new Pdf();
    $filename = $evaluation['evaluationDescription'] . '-Repair-Plan-Drawing.pdf';

    if (count($evaluationDrawings) > 0) {
        // repair plan pro pdfs
        $drawingResource = DrawingResource::make(Acl::make($evaluationDrawings[0]->user));

        foreach ($evaluationDrawings as $drawing) {
            try {
                $drawingID = Uuid::fromBytes($drawing->drawingID)->toString();
                $pdf->addFile($drawingResource->getMediaHandler('repair_plan')->generate($drawingID));
            } catch (Exception $e) {
                Log::create('legacy_app_drawing')->error('Unable to generate drawing', [
                    'exception' => $e,
                    'evaluation_id' => $evaluationID,
                    'drawing_id' => isset($drawingID) ? $drawingID : null,
                    'user_id' => $userID
                ]);
            }
        }
    }

    // repair plan pdfs
    if (!empty($appDrawing->UUIDS)) {
        $pdf = $appDrawing->appendDrawing($pdf);
    }

    if (!$pdf->send($filename, true)) {
        throw new AppException('Unable to stream drawing PDF: %s', $pdf->getError());
    }

} else {
    echo('No drawings to display');
}
