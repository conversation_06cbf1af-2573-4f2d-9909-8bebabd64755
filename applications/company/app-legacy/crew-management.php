<?php

use Core\StaticAccessors\Config;

	//if (isset($_POST["projectSearch"])) {
	//	$projectSearch = preg_replace('#[^A-Za-z0-9., @_ ]#i', '', $_POST["projectSearch"]); 
	//	header('location: search.php?search='.$projectSearch.'');
	//}

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID'];
	}	

	$todayDate = date('l, F j, Y');
	$todaysDateDefault = date('Y-m-d');
	$companyProfileDisplay = NULL;
	$setupDisplay = NULL;
	$customerAddDisplay = NULL;
	$metricsNavDisplay = NULL;
	$crewManagementNavDisplay = NULL;
	$accountDisplay = NULL;
	$notificationsCountDisplay = NULL;
	$marketingNavDisplay = NULL;
	$brandingStylesheet = NULL;
	$brandingFavicon = NULL;
		
	include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$userFirstName = $userArray['userFirstName'];
		$userLastName = $userArray['userLastName'];
		$userPhoneDirect = $userArray['userPhoneDirect'];
		$userPhoneCell = $userArray['userPhoneCell'];
		$userEmail = $userArray['userEmail'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$marketing = $userArray['marketing'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timecardApprover = $userArray['timecardApprover'];
		$calendarBgColor = $userArray['calendarBgColor'];
		$companyLatitude = $userArray['latitude'];  //FXLRATR-258
		$companyLongitude = $userArray['longitude']; //FXLRATR-258
		$companyAddress1 = $userArray['address']; //FXLRATR-258
		$companyAddress2 = $userArray['address2']; //FXLRATR-258
		$companyCity = $userArray['city']; //FXLRATR-258
		$companyState = $userArray['state']; //FXLRATR-258
		$companyZip = $userArray['zip']; //FXLRATR-258

        $lists = Config::get('lists');
		
?>