<?php

use App\Services\TimeService;
use Core\StaticAccessors\App;
	
	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

    /** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];
	
	
	if (isset($_GET['projectID'])) {
		 $projectID = filter_input(INPUT_GET, 'projectID', FILTER_SANITIZE_NUMBER_INT);
	}

	include_once(__DIR__ . '/../includes/classes/class_ProjectNotes.php');
			
    $object = new Notes();
    $object->setProject($projectID, $companyID);
    $object->getProject();

    $noteArray = $object->getResults();

    if ($noteArray != '') {
        foreach ( $noteArray as $k=>$v ) {
            if ($noteArray[$k] ['noteAdded'] != NULL) {
                $noteArray[$k]['noteAdded'] = $time_service->getFromUtc($noteArray[$k]['noteAdded'])->format('n/j/Y g:i a');
            }

            if ($noteArray[$k] ['noteEdited'] != NULL) {
                $noteArray[$k]['noteEdited'] = $time_service->getFromUtc($noteArray[$k]['noteEdited'])->format('n/j/Y g:i a');
            }
        }
    }
    echo json_encode($noteArray);
?>