<?php

use App\Classes\FX\Report;
use App\Services\TimeService;
use Carbon\Carbon;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Input;
use Core\StaticAccessors\App;

/** @var TimeService $time_service */
$time_service = App::get(TimeService::class);

$user = Auth::user();

$type = (int) Input::post('type', 0);

$dateFields = [
    'startDate' => 'Start Date',
    'endDate' => 'End Date'
];
$dates = [];
foreach ($dateFields as $key => $label) {
    $date = trim(Input::post($key, ''));
    if (strlen($date) === 0) {
        throw new Exception(sprintf('%s not specified', $label));
    }
    if (preg_match('#^(?<month>[0-9]{1,2})/(?<day>[0-9]{1,2})/(?<year>[0-9]{4})$#', $date, $match) !== 1) {
        throw new Exception(sprintf('%s is not valid', $label));
    }
    $dates[$key] = "{$match['year']}-{$match['month']}-{$match['day']}";
}

if (($class = Report::getClassByType($type)) === null) {
    throw new Exception('Invalid report type');
}

$start_date = $time_service->get($dates['startDate'])->startOfDay();
$end_date = $time_service->get($dates['endDate'])->endOfDay();

$data = (new $class)
    ->setCompanyID($user->companyID)
    ->setStartDate($time_service->getUtc($start_date))
    ->setEndDate($time_service->getUtc($end_date))
    ->run();

header('Content-Type: application/json');
echo json_encode($data);
