<?php

use App\Classes\Log;
use App\Services\CompanySettingService;
use App\Services\Quickbooks\Resources\CreditMemoResource;
use App\Services\Quickbooks\Resources\CustomerResource;
use App\Services\Quickbooks\Resources\InvoiceResource;
use App\Services\QuickbooksService;

$log = Log::create('quickbooks_invoice_number', [
    'file' => 'quickbooks.log'
]);
	
	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];

		$setting_service = new CompanySettingService((int) $companyID);
		$quickbooksDefaultService = $setting_service->get('quickbooks_default_service');

		if ($primary == 1 || $sales == 1 || $projectManagement == 1) {

			if(isset($_POST['evaluationID'])) {
				$evaluationID = filter_input(INPUT_POST, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
			}


			if(isset($_POST['bidID'])) {
				 $bidID = filter_input(INPUT_POST, 'bidID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
			}


			if(isset($_POST['custom'])) {
				 $customEvaluation = filter_input(INPUT_POST, 'custom', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
			}

			if(isset($_POST['quickbooksID'])) {
				$quickbooksID = filter_input(INPUT_POST, 'quickbooksID', FILTER_SANITIZE_NUMBER_INT);
			}
			
			if(isset($_POST['scopeChange'])) {
				 $scopeChanges = filter_input(INPUT_POST, 'scopeChange', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
			}
			
			if(isset($_POST['changeTotal'])) {
				 $scopeChangeTotal = filter_input(INPUT_POST, 'changeTotal', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
			}

			if(isset($_POST['changeType'])) {
				 $scopeChangeType = filter_input(INPUT_POST, 'changeType', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
			}

			if ($scopeChangeType == 'invoice') {
				$scopeChangeType = '0';
			} 
			else if ($scopeChangeType == 'creditMemo') {
				$scopeChangeType = '1';
			}
				
			include(__DIR__ . '/../includes/classes/class_EditScopeChangeItems.php');

			$allLineItems = array();

			foreach ( $scopeChanges as $k=>$v ){
				$lineItem = array();

				$scopeChangeItemID = $scopeChanges[$k] ['changeID'];
				$sort = $scopeChanges[$k] ['changeSort'];
				$date = $scopeChanges[$k] ['changeDate'];
				$date = date('Y-m-d', strtotime($date)); 
				$item = $scopeChanges[$k] ['changeItem'];
				$price = $scopeChanges[$k] ['changePrice'];
				$type = $scopeChanges[$k] ['changeType'];

				if (array_key_exists('changeDelete', $scopeChanges[$k])) {
					$changeDelete = $scopeChanges[$k] ['changeDelete'];
				}
				else {
					$changeDelete = null;
				}

				if ($price != '' && $item != '') {
					if ($scopeChangeType == 1){
						if ($price < 0) {
							$newPrice = abs($price);
						} 
						else {
							$newPrice =  0 - $price;
						}
					} 
					else {
						$newPrice = $price;  
					}
					$lineItem['amount'] = $newPrice;
					$lineItem['description'] = $item;
					$lineItem['item'] = $quickbooksDefaultService;

					if ($changeDelete === null) {
						$allLineItems[] = $lineItem;
					}
				}

				if ($type == 'charge') {
					$type = 0;
				} 
				else if ($type == 'credit') {
					$type = 1;
				}
			
				$object = new ScopeChangeItems();
				$object->setEvaluation($evaluationID, $scopeChangeItemID, $sort, $date, $item, $price, $type, $changeDelete); 
				$object->sendEvaluation();		
			}	

			include_once(__DIR__ . '/../includes/classes/class_EvaluationProject.php');
			
				$object = new EvaluationProject();
				$object->setEvaluation($evaluationID, $companyID, $customEvaluation);
				$object->getEvaluation();
				$projectArray = $object->getResults();	

				//Project
				$bidScopeChangeTotal = $projectArray['bidScopeChangeTotal'];
				$bidScopeChangeType = $projectArray['bidScopeChangeType'];
				$bidScopeChangeQuickbooksID = $projectArray['bidScopeChangeQuickbooksID'];
				$bidScopeChangeNumber = $projectArray['bidScopeChangeNumber'];
				$bidScopeChangeQuickbooks = $projectArray['bidScopeChangeQuickbooks'];
				$email = $projectArray['email'];

			$quickbooks_service = new QuickbooksService((int) $companyID);

			if ($quickbooks_service->isConnected()){
		
				if ($quickbooksID == '') {
					
					include_once(__DIR__ . '/../includes/classes/class_EvaluationProject.php');
			
					$object = new EvaluationProject();
					$object->setEvaluation($evaluationID, $companyID, $customEvaluation);
					$object->getEvaluation();
					$projectArray = $object->getResults();	

					//Project
					$customerID = $projectArray['customerID'];

					try {
						$customer = \Common\Models\Customer::find($customerID);
						$customer_resource = new CustomerResource($quickbooks_service);
						$customer->quickbooksID = $customer_resource->createFromModel($customer);
						$customer->save();
					} catch (\Throwable $e) {
						$log->error('Unable to create customer', [
							'exception' => $e,
							'customer_id' => $customerID
						]);
					}
				}


				if ($quickbooksID == '') {
				  echo '{"msg":"Quickbooks ID is empty"}';
				  exit();
				}

				include_once(__DIR__ . '/../includes/classes/class_CheckInvoiceNumber.php');

				$invoice_resource = new InvoiceResource($quickbooks_service);
				$credit_memo_resource = new CreditMemoResource($quickbooks_service);

				$customInvoiceNumber = null;
				$lastInvoiceNumber = $invoice_resource->getLastInvoiceNumber();

				//If No Invoices Exist in Quickbooks Then Get Last Invoice Number From Our System
				if (empty($lastInvoiceNumber)) {
					include_once(__DIR__ . '/../includes/classes/class_LastInvoiceNumber.php');

					$object = new LastInvoiceNumber();
					$object->setCompany($bidID);
					$object->getCompany();
					$lastInvoice = $object->getResults();

					if (!empty($lastInvoice)) {
						foreach($lastInvoice as &$row) {
							$lastInvoiceNumber = $row['invoiceNumber'];
						}
					} else {
						$lastInvoiceNumber = '1000';
					}
                    $customInvoiceNumber = $lastInvoiceNumber;
				}

                $scopeChangeID = null;
                $scopeChangeNumber = null;
                $scopeChangeQuickbooks = null;

                // if invoice scope change
				if ($scopeChangeType == 0){
					//Only Add Invoice if its not been created, Otherwize Delete or Edit

					//Create New Invoice
					if ($bidScopeChangeNumber == '') {
						try {
							$scopeChangeResult = $invoice_resource->legacyCreate($quickbooksID, $customInvoiceNumber, $allLineItems, $email);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $scopeChangeResult['invoiceID'];
							$scopeChangeNumber = $scopeChangeResult['invoiceNumber'];
						} catch (\Throwable $e) {
							$log->error('Scope Change invoice has not been created', [
								'exception' => $e,
								'newInvoiceNumber' => $customInvoiceNumber,
								'evaluationID' => $evaluationID,
							]);
						}
					} elseif (count($allLineItems) === 0) {
						try {
							$invoice_resource->delete($bidScopeChangeQuickbooksID);
						} catch (\Throwable $e) {
							$log->error('Unable to delete invoice', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}
					}
					elseif ($bidScopeChangeNumber != '' && $bidScopeChangeQuickbooks == 1 && $bidScopeChangeType == 1) { // if credit memo scope change
						try {
							$credit_memo_resource->delete($bidScopeChangeQuickbooksID);
						} catch (\Throwable $e) {
							$log->error('Unable to delete credit memo', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}

						//Create New Invoice
						try {
							$scopeChangeResult = $invoice_resource->legacyCreate($quickbooksID, $customInvoiceNumber, $allLineItems, $email);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $scopeChangeResult['invoiceID'];
							$scopeChangeNumber = $scopeChangeResult['invoiceNumber'];
						} catch (\Throwable $e) {
							$log->error('Scope Change invoice has not been created', [
								'exception' => $e,
								'newInvoiceNumber' => $customInvoiceNumber,
								'evaluationID' => $evaluationID
							]);
						}
					} 
					elseif ($bidScopeChangeNumber != '' && $bidScopeChangeQuickbooks == 1 && $bidScopeChangeType == 0) {
						//Update Existing Invoice in Quickbooks
						try {
							$invoice_resource->legacyUpdate($bidScopeChangeQuickbooksID, $allLineItems);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $bidScopeChangeQuickbooksID;
							$scopeChangeNumber = $bidScopeChangeNumber;
						} catch (\Throwable $e) {
							$log->error('Unable to update invoice', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}
					}
				} 
				elseif ($scopeChangeType == 1){ // if credit memo scope change
					function array_sort_by_column(&$arr, $col, $dir = SORT_DESC) {
						  $sort_col = array();
						  foreach ($arr as $key=> $row) {
						      $sort_col[$key] = $row[$col];
						  }
						  array_multisort($sort_col, $dir, $arr);
					}

					array_sort_by_column($allLineItems, 'amount');

					//Only Add Memo if its not been created, Otherwize Delete or Edit
					//Create Credit Memo
					if ($bidScopeChangeNumber == '') {
						try {
							$scopeChangeResult = $credit_memo_resource->legacyCreate($quickbooksID, $customInvoiceNumber, $allLineItems, $email);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $scopeChangeResult['creditID'];
							$scopeChangeNumber = $scopeChangeResult['creditNumber'];
						} catch (\Throwable $e) {
							$log->error('Scope Change credit memo has not been created', [
								'exception' => $e,
								'newInvoiceNumber' => $customInvoiceNumber,
								'evaluationID' => $evaluationID
							]);
						}
					} elseif (count($allLineItems) === 0) {
						try {
							$credit_memo_resource->delete($bidScopeChangeQuickbooksID);
						} catch (\Throwable $e) {
							$log->error('Unable to delete credit memo', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}
					}
					elseif ($bidScopeChangeNumber != '' && $bidScopeChangeQuickbooks == 1 && $bidScopeChangeType == 0) {
						//Delete Invoice
						try {
							$invoice_resource->delete($bidScopeChangeQuickbooksID);
						} catch (\Throwable $e) {
							$log->error('Unable to delete invoice', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}

						//Create New Credit Memo
						try {
							$scopeChangeResult = $credit_memo_resource->legacyCreate($quickbooksID, $customInvoiceNumber, $allLineItems, $email);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $scopeChangeResult['creditID'];
							$scopeChangeNumber = $scopeChangeResult['creditNumber'];
						} catch (\Throwable $e) {
							$log->error('Scope Change credit memo has not been created', [
								'exception' => $e,
								'newInvoiceNumber' => $customInvoiceNumber,
								'evaluationID' => $evaluationID
							]);
						}
					}
					elseif ($bidScopeChangeNumber != '' && $bidScopeChangeQuickbooks == 1 && $bidScopeChangeType == 1) {
						//Update Existing Credit Memo in Quickbooks
						try {
							$credit_memo_resource->legacyUpdate($bidScopeChangeQuickbooksID, $allLineItems);
							$scopeChangeQuickbooks = 1;
							$scopeChangeID = $bidScopeChangeQuickbooksID;
							$scopeChangeNumber = $bidScopeChangeNumber;
						} catch (\Throwable $e) {
							$log->error('Unable to edit credit memo', [
								'exception' => $e,
								'quickbooks_id' => $bidScopeChangeQuickbooksID
							]);
						}
					}
				}
			} 
			else {
				if ($bidScopeChangeNumber == '') {

					include_once(__DIR__ . '/../includes/classes/class_LastInvoiceNumber.php');

					$object = new LastInvoiceNumber();
					$object->setCompany($bidID);
					$object->getCompany();
					$lastInvoiceNumber = $object->getResults();

					foreach($lastInvoiceNumber as &$row) {
						
						$lastNumber = $row['invoiceNumber'];

						$scopeChangeID = NULL;
						$scopeChangeNumber = (is_numeric($lastNumber) ? $lastNumber : 0) + 1;
						$scopeChangeQuickbooks = NULL;

					}

				} 
				else {
					$scopeChangeID = NULL;
					$scopeChangeNumber = $bidScopeChangeNumber;
					$scopeChangeQuickbooks = NULL;
				}
			}

			if (count($allLineItems) === 0) {
				$scopeChangeTotal = null;
				$scopeChangeType = null;
				$scopeChangeID = null;
				$scopeChangeNumber = null;
				$scopeChangeQuickbooks = null;
			}

				include_once(__DIR__ . '/../includes/classes/class_EditEvaluationScopeChange.php');
					
				$object = new ScopeChange();
				$object->setEvaluation($evaluationID, $scopeChangeTotal, $scopeChangeType, $scopeChangeID, $scopeChangeNumber, $scopeChangeQuickbooks);
				$object->sendEvaluation();
				$result = $object->getResults();

				echo json_encode($result);
		}
?>
