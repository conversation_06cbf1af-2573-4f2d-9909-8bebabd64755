<?php

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	

		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];

	include_once(__DIR__ . '/../includes/classes/class_GetMarketingTypes.php');
			
		$object = new GetMarketingTypes();
		$object->setCompanyID($companyID);
		$object->getMarketingTypes();
		$results = $object->getResults();	
		echo json_encode($results);		
?>