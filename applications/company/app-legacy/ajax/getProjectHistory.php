<?php

use App\Services\TimeService;
use Core\StaticAccessors\App;

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	/** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];


		if(isset($_GET['projectID'])) {
			$projectID = filter_input(INPUT_GET, 'projectID', FILTER_SANITIZE_NUMBER_INT);
		}

		$historyArray = array();
	
	
	//Get Project History
	include_once(__DIR__ . '/../includes/classes/class_ProjectHistory.php');
			
		$object = new ProjectHistory();
		$object->setProject($projectID, $companyID);
		$object->getProject();
		
		$projectHistoryArray = $object->getResults();	
		
		if (!empty($projectHistoryArray)) $historyArray = array_merge($historyArray, $projectHistoryArray);
	
		
	//Get Project History Evaluations
	include_once(__DIR__ . '/../includes/classes/class_ProjectHistoryEvaluation.php');
			
		$object = new ProjectHistoryEvaluation();
		$object->setProject($projectID, $companyID);
		$object->getProject();
		
		$projectHistoryEvalArray = $object->getResults();	
		
		if (!empty($projectHistoryEvalArray)) $historyArray = array_merge($historyArray, $projectHistoryEvalArray);	


	//Get Project History Schedule
	include_once(__DIR__ . '/../includes/classes/class_ProjectHistorySchedule.php');
			
		$object = new ProjectHistorySchedule();
		$object->setProject($projectID, $companyID);
		$object->getProject();
		
		$projectHistorySchedArray = $object->getResults();	
		
		if (!empty($projectHistorySchedArray)) $historyArray = array_merge($historyArray, $projectHistorySchedArray);		
	
	
	//Filter Notification Type
	if ($historyArray != '') {
		
		foreach($historyArray as $k => $v) {
			
			if ($historyArray[$k]['historyType'] == 'project' || $historyArray[$k]['historyType'] == 'evaluation') {
                $historyArray[$k]['date'] = $time_service->getFromUtc($historyArray[$k]['date'])->format('n/j/Y g:i a');

			} else if ($historyArray[$k]['historyType'] == 'schedule') {

                $historyArray[$k]['date'] = $time_service->getFromUtc($historyArray[$k]['date'])->format('n/j/Y g:i a');

				$historyArray[$k]['startDate'] = date('n/j/Y g:i a', strtotime($historyArray[$k]['startDate']));

				$historyArray[$k]['endDate'] = date('n/j/Y g:i a', strtotime($historyArray[$k]['endDate']));

			}
		}
			
	}

	function date_compare($a, $b)
	{
	    $t1 = strtotime($a['date']);
	    $t2 = strtotime($b['date']);
	    return $t2 - $t1;
	}    
	usort($historyArray, 'date_compare');
	

	echo json_encode($historyArray);			
?>