<?php

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];


		if ($primary == 1) {
            if (isset($_GET['warrantyID'])) {
                $warrantyID = filter_input(INPUT_GET, 'warrantyID', FILTER_SANITIZE_NUMBER_INT);
            }

            include_once(__DIR__ . '/../includes/classes/class_WarrantyDocument.php');

            $object = new Warranty();
            $object->setWarranty($companyID, $warrantyID);
            $object->getWarranty();
            $warrantyArray = $object->getResults();

            if ($warrantyArray != '') {
                foreach ( $warrantyArray as $k=>$v ) {
                    $temp = htmlspecialchars_decode($warrantyArray[$k]['warranty']);
                    $warrantyArray[$k]['warranty'] = $temp;
                }

            }
            echo json_encode($warrantyArray);
		}
?>