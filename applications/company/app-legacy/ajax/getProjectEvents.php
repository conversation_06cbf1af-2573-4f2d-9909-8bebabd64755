<?php

use App\Services\TimeService;
use Core\StaticAccessors\App;

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

    /** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];
		
	
	if (isset($_GET['projectID'])) {
		$projectID = filter_input(INPUT_GET, 'projectID', FILTER_SANITIZE_NUMBER_INT);
	}
		
	include_once(__DIR__ . '/../includes/classes/class_ProjectSchedule.php');
			
		$object = new ProjectSchedule();
		$object->setProject($projectID, $companyID);
		$object->getSchedule();
		$scheduleArray = $object->getResults();	
		
		if ($scheduleArray != '') {
		
			foreach ( $scheduleArray as $k=>$v ) {
				$scheduleArray[$k] ['scheduledStart'] = date('n/j/Y g:i a', strtotime($scheduleArray[$k] ['scheduledStart']));
				$scheduleArray[$k] ['scheduledStartDate'] = date('n/j/Y', strtotime($scheduleArray[$k] ['scheduledStart']));
				$scheduleArray[$k] ['scheduledStartTime'] = date('g:i a', strtotime($scheduleArray[$k] ['scheduledStart']));
				if (date('g:i a', strtotime($scheduleArray[$k] ['scheduledStart'])) === '12:00 am') {
                    $scheduleArray[$k] ['scheduledStart'] = date('n/j/Y', strtotime($scheduleArray[$k] ['scheduledStart']));
                }
				
				$scheduleArray[$k] ['scheduledEnd'] = date('n/j/Y g:i a', strtotime($scheduleArray[$k] ['scheduledEnd']));
				$scheduleArray[$k] ['scheduledEndDate'] = date('n/j/Y', strtotime($scheduleArray[$k] ['scheduledEnd']));
				$scheduleArray[$k] ['scheduledEndTime'] = date('g:i a', strtotime($scheduleArray[$k] ['scheduledEnd']));
                if (date('g:i a', strtotime($scheduleArray[$k] ['scheduledEnd'])) === '11:59 pm') {
                    $scheduleArray[$k] ['scheduledEnd'] = date('n/j/Y', strtotime($scheduleArray[$k] ['scheduledEnd']));
                }

                $scheduleArray[$k]['scheduledOn'] = $time_service->getFromUtc($scheduleArray[$k]['scheduledOn'])->format('n/j/Y g:i a');

                if (!empty($scheduleArray[$k]['cancelledDate'])) {
                    $scheduleArray[$k]['cancelledDate'] = $time_service->getFromUtc($scheduleArray[$k]['cancelledDate'])->format('n/j/Y g:i a');
                }

                if (!empty($scheduleArray[$k]['replacedAt'])) {
                    $scheduleArray[$k]['replacedAt'] = $time_service->getFromUtc($scheduleArray[$k]['replacedAt'])->format('n/j/Y g:i a');
                }
				
				if (!empty($scheduleArray[$k] ['installationComplete']) && !empty($scheduleArray[$k] ['installationCompleteRecordedDT'])) {
                    $scheduleArray[$k]['installationCompleteRecordedDT'] = $time_service->getFromUtc($scheduleArray[$k]['installationCompleteRecordedDT'])->format('n/j/Y g:i a');
				}

                if (!empty($scheduleArray[$k] ['pendingAt']) && !empty($scheduleArray[$k] ['pendingAt'])) {
                    $scheduleArray[$k]['pendingAt'] = $time_service->getFromUtc($scheduleArray[$k]['pendingAt'])->toIso8601String();
                }
			}
		}
		echo json_encode($scheduleArray);
?>