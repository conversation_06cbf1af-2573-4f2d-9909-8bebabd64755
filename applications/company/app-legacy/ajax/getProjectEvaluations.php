<?php

use App\Services\TimeService;
use Common\Models\EvaluationNewDrawing;
use Core\StaticAccessors\App;

    use App\Classes\FX\AppDrawing;
    include_once(__DIR__ . '/../includes/classes/class_User.php');

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

    /** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];

	
	if (isset($_GET['projectID'])) {
		$projectID = filter_input(INPUT_GET, 'projectID', FILTER_SANITIZE_NUMBER_INT);
	}
		
	include_once(__DIR__ . '/../includes/classes/class_ProjectEvaluation.php');
			
		$object = new Evaluation();
		$object->setProject($projectID);
		$object->getEvaluation();
		$evaluations = $object->getResults();

        $evaluationArray = [];

        if (is_array($evaluations)) {
            foreach ($evaluations as $evaluation) {
                if ($evaluation['companyID'] === $companyID) {
                    $evaluationArray[] = $evaluation;
                }
            }
        }

		if ($evaluationArray != '') {
		
			foreach ( $evaluationArray as $k=>$v )
			{
				if ($evaluationArray[$k]['importedDate'] != NULL) {
                    $evaluationArray[$k]['importedDate'] = $time_service->getFromUtc($evaluationArray[$k]['importedDate'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['evaluationCreated'] != NULL) {
                    $evaluationArray[$k]['evaluationCreated'] = $time_service->getFromUtc($evaluationArray[$k]['evaluationCreated'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['evaluationLastUpdated'] != NULL) {
                    $evaluationArray[$k]['evaluationLastUpdated'] = $time_service->getFromUtc($evaluationArray[$k]['evaluationLastUpdated'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['evaluationCancelled'] != NULL) {
                    $evaluationArray[$k]['evaluationCancelled'] = $time_service->getFromUtc($evaluationArray[$k]['evaluationCancelled'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['evaluationFinalized'] != NULL) {
                    $evaluationArray[$k]['evaluationFinalized'] = $time_service->getFromUtc($evaluationArray[$k]['evaluationFinalized'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['finalReportSent'] != NULL) {
                    $evaluationArray[$k]['finalReportSent'] = $time_service->getFromUtc($evaluationArray[$k]['finalReportSent'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['bidFirstSent'] != NULL) {
                    $evaluationArray[$k]['bidFirstSent'] = $time_service->getFromUtc($evaluationArray[$k]['bidFirstSent'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['bidLastSent'] != NULL) {
                    $evaluationArray[$k]['bidLastSent'] = $time_service->getFromUtc($evaluationArray[$k]['bidLastSent'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['bidLastViewed'] != NULL) {
                    $evaluationArray[$k]['bidLastViewed'] = $time_service->getFromUtc($evaluationArray[$k]['bidLastViewed'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['bidAccepted'] != NULL) {
                    $evaluationArray[$k]['bidAccepted'] = $time_service->getFromUtc($evaluationArray[$k]['bidAccepted'])->format('n/j/Y g:i a');
				}

				if ($evaluationArray[$k]['bidRejected'] != NULL) {
                    $evaluationArray[$k]['bidRejected'] = $time_service->getFromUtc($evaluationArray[$k]['bidRejected'])->format('n/j/Y g:i a');
				}

                if ($evaluationArray[$k]['submittedAt'] != NULL) {
                    $evaluationArray[$k]['submittedAt'] = $time_service->getFromUtc($evaluationArray[$k]['submittedAt'])->format('n/j/Y g:i a');
                }

                if ($evaluationArray[$k]['evaluationID'] != NULL) {
				    $evaluationID = $evaluationArray[$k]['evaluationID'];
                    $evaluationArray[$k]['appDrawingExists'] = false;

                    $appDrawing = new AppDrawing();
                    $appDrawing->getAllDrawings($evaluationID);
                    if (!empty($appDrawing->UUIDS)) {
                        $evaluationArray[$k]['appDrawingExists'] = true;
                    }

                    if (EvaluationNewDrawing::where('evaluationID', $evaluationID)->count() > 0) {
                        $evaluationArray[$k]['appDrawingExists'] = true;
                    }
                }
            }
		}
	
		echo json_encode($evaluationArray);