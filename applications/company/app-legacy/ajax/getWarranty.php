<?php

use App\Services\TimeService;
use Core\StaticAccessors\App;

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

    /** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];


        if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
				
			include_once(__DIR__ . '/../includes/classes/class_Warranty.php');
					
            $object = new Warranty();
            $object->setCompany($companyID);
            $object->getCompany();
            $warrantyArray = $object->getResults();

            if ($warrantyArray != '') {

                foreach ($warrantyArray as $k=>$v ) {

                    if ($warrantyArray[$k]['lastUpdated'] != NULL) {
                        $warrantyArray[$k]['lastUpdated'] = $time_service->getFromUtc($warrantyArray[$k]['lastUpdated'])->format('n/j/Y g:i a');
                    }

                    if ($warrantyArray[$k]['type'] == 0) {
                        $warrantyArray[$k]['type'] = "Certificate";
                    } else {
                        $warrantyArray[$k]['type'] = "Document";
                    }
                }
            }

            echo json_encode($warrantyArray);
		}
?>