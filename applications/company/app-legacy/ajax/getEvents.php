<?php

use App\Classes\GoogleMap;
use App\Services\CompanySettingService;
use Carbon\Carbon;
use Common\Models\ProjectSchedule;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;

    $user = Auth::user();
    $settingService = new CompanySettingService($user->companyID);
    $calendarSharing = true;

    if (!$settingService->get('allow_calendar_sharing', false) && !$user->primary && !$user->projectManagement && ($user->installation || $user->sales)) {
        $calendarSharing = false;
    }

	// Require our Event class and datetime utilities
	require(__DIR__ . '/../includes/dateTimeUtility.php');

	$filter = NULL;
    $user_filter = '';
    $project_filter = '';
		
	if (isset($_GET['filter'])) {
        $filter = filter_input(INPUT_GET, 'filter', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
        if (!empty($filter)) {
            $filter = array_filter($filter, function($value) {
                return is_numeric($value);
            });
        }
	} 	
		
	if(isset($_GET['start'])) {
		$dateStart = filter_input(INPUT_GET, 'start', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}
	
	if(isset($_GET['end'])) {
		$dateEnd = filter_input(INPUT_GET, 'end', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}
		
	// Parse the timezone parameter if it is present.
	$timezone = null;
	if (isset($_GET['timezone'])) {
		$timezone = new DateTimeZone($_GET['timezone']);
	}

    if ($calendarSharing && !empty($filter)) {
        $filter = implode(",",$filter);
        $user_filter = 'AND s.userID IN ('.$filter.')';
        $project_filter = 'AND s.scheduledUserID IN ('.$filter.')';
    } else if (!$calendarSharing) {
        $user_filter = 'AND s.userID IN ('.$user->userID.')';
        $project_filter = 'AND s.scheduledUserID IN ('.$user->userID.')';
    }

    $calendarArray = DB::select('
        (SELECT 1 AS eventType, p.projectID, s.projectScheduleID, s.status AS projectScheduleStatus, m.firstName, m.lastName, m.businessName, t.latitude,
        t.longitude, s.scheduleType, s.description, s.scheduledStart, s.scheduledEnd, s.isAllDay, s.sendNotifications, s.scheduledUserID,
        u.userFirstName AS resourceFirstName, u.userLastName AS resourceLastName, u.calendarBgColor, u.calendarTextColor,
        t.address, t.address2, t.city, t.state, t.zip, p.projectDescription 
                
        FROM projectSchedule AS s
        
        LEFT JOIN project AS p ON p.projectID = s.projectID 
        LEFT JOIN property AS t ON t.propertyID = p.propertyID
        LEFT JOIN customer AS m ON m.customerID = t.customerID
        LEFT JOIN companies AS c ON c.companyID = m.companyID 
        LEFT JOIN user AS u ON u.userID = s.scheduledUserID 
    
        WHERE c.companyID=:projectCompanyID AND s.scheduledStart < :projectDateEnd AND s.scheduledEND >= :projectDateStart '.$project_filter.' AND s.cancelledAt IS NULL AND s.replacedAt IS NULL AND s.deletedAt IS NULL)
                        
        UNION ALL
   
        (SELECT 2 AS eventType, null, s.userScheduleID, NULL as projectScheduleStatus, u.userFirstName, u.userLastName, NULL, NULL, NULL, s.scheduleType,
        NULL, s.scheduledStart, s.scheduledEnd, NULL as isAllDay, NULL as sendNotifications, s.userID, u.userFirstName, u.userLastName, u.calendarBgColor, u.calendarTextColor, NULL, NULL,
        NULL, NULL, NULL, NULL 
        
        FROM userSchedule AS s
            
        LEFT JOIN user AS u ON u.userID = s.userID 
        LEFT JOIN companies AS c ON c.companyID = u.companyID 
    
        WHERE c.companyID=:userCompanyID AND s.scheduledStart < :userDateEnd AND s.scheduledEND >= :userDateStart '.$user_filter.' AND s.cancelledDate IS NULL)
              
        UNION ALL

        (SELECT 3 AS eventType, s.companyID, s.companyScheduleID, NULL as projectScheduleStatus, c.name, NULL, NULL, NULL, NULL, s.scheduleType, NULL,
        s.scheduledStart, s.scheduledEnd, NULL as isAllDay, NULL as sendNotifications, 0, c.name, NULL, "#888888" AS calendarBgColor, "#ffffff" AS calendarTextColor,
        NULL, NULL, NULL, NULL, NULL, NULL 
        
        FROM companySchedule AS s
            
        LEFT JOIN companies AS c ON c.companyID = s.companyID 
                    
        WHERE c.companyID=:companyID AND s.scheduledStart < :companyDateEnd AND s.scheduledEND >= :companyDateStart AND s.cancelledDate IS NULL)

        ORDER BY scheduledUserID ASC, scheduledStart ASC', [
            'projectCompanyID' => $user->companyID,
            'projectDateStart' => $dateStart,
            'projectDateEnd' => $dateEnd,
            'userCompanyID' => $user->companyID,
            'userDateStart' => $dateStart,
            'userDateEnd' => $dateEnd,
            'companyID' => $user->companyID,
            'companyDateStart' => $dateStart,
            'companyDateEnd' => $dateEnd
        ]);

		if (!empty($calendarArray)) {

			foreach ($calendarArray as $k => $v) {
			    $event = $calendarArray[$k];

			    if ($event->eventType == 2 || $event->eventType == 3 || (int) $event->projectScheduleStatus !== ProjectSchedule::STATUS_ACTIVE) {
                    $event->editable = false;
                }

                $event->id = $event->projectScheduleID;

                $event->title = $event->firstName . ' ' . $event->lastName;
				if ($event->businessName != NULL){
                    $event->title = $event->businessName;
                }

                $event->start = $event->scheduledStart;
                $event->end = $event->scheduledEnd;

                if ($event->eventType == 1) {
                    $event->directions = GoogleMap::directionsUrl($event->address . ' ' . $event->city . ' ' . $event->state . ' ' . $event->zip);
                }

                if ((bool) $event->isAllDay) {
                    $event->allDay = 1;
                    $end = Carbon::parse($event->end);
                    // move end date to next day at midnight since fullcalendar library end date is exclusive
                    $event->end = $end->addDay()->startOfDay()->toDateTimeString();
                } elseif (date('H:i:s', strtotime($event->start)) == '00:00:00' && date('H:i:s', strtotime($event->end)) == '00:00:00') {
                    $event->allDay = 1;
				}

                $event->resourceId = $event->scheduledUserID;
			}
		}
		echo json_encode($calendarArray);
?>