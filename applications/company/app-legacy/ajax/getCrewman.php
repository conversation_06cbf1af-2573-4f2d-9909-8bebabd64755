<?php

$user = Auth::user();

if(isset($_GET["crewmanID"])) {
    $crewmanID = $_GET['crewmanID'];
}

if ($user->primary == 1 || $user->timecardApprover == 1) {
    $hourly = 0;
    if ($user->primary == 1) {
        $hourly = 1;
    }

    include_once(__DIR__ . '/../includes/classes/class_GetCrewman.php');

    $object = new GetCrewman();
    $object->setCrewmanID($crewmanID);
    $object->setCompanyID($user->companyID);
    $object->getCrewman($hourly);
    $crewmanArray = $object->getResults();

    if (array_key_exists("hourlyPayRate",$crewmanArray)) {
        $crewmanArray['canViewHourly'] = 1;
    } else {
        $crewmanArray['canViewHourly'] = 0;
    }

    echo json_encode($crewmanArray);
} else {
    echo json_encode([
        'response' => 'You do not have the correct permissions.'
    ]);
}

?>