<?php

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		unset($userArray['emailAddCustomer'], $userArray['emailSchedule'], $userArray['emailBidSent'],
		    $userArray['emailInstallation'], $userArray['emailBidAccept'], $userArray['emailBidReject'],
		    $userArray['emailFinalPacket'], $userArray['emailInvoice'], $userArray['emailSchedule']);

		echo json_encode($userArray);
?>