<?php

use App\Classes\GoogleMap;
use App\Services\TimeService;
use Core\StaticAccessors\App;
use Core\Components\DB\StaticAccessors\DB;
use Core\Exceptions\AppException;

	if (isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	}

    /** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];
	
	if (isset($_GET['eventID'])) {
		$eventID = filter_input(INPUT_GET, 'eventID', FILTER_SANITIZE_NUMBER_INT);
	}
		
	if (isset($_GET['eventType'])) {
		$eventType = filter_input(INPUT_GET, 'eventType', FILTER_SANITIZE_NUMBER_INT);
	}

    if (!empty($companyID) && !empty($eventType)) {

        switch ($eventType) {
            case '1':
                $event = collect(DB::select('
                    SELECT s.projectScheduleID, s.projectID,
                    s.scheduledUserID, s.scheduleType, s.description, s.scheduledStart,
                    s.scheduledEnd, s.createdByUserID AS scheduledByUserID, s.isAllDay,
                    t.address, t.address2, t.city, t.state, t.zip, t.latitude,
                    t.longitude, m.firstName, m.lastName, m.email, m.businessName, h.phoneNumber,
                    u.userFirstName AS scheduledFirstName, u.userLastName AS
                    scheduledLastName, e.userFirstName AS scheduledByFirstName,
                    e.userLastName AS scheduledByLastName, s.createdAt AS scheduledOn, p.projectDescription

                    FROM projectSchedule AS s

                    JOIN project AS p ON p.projectID = s.projectID
                    JOIN property AS t ON t.propertyID = p.propertyID
                    JOIN customer AS m ON m.customerID = t.customerID
                    LEFT JOIN customerPhone AS h ON h.customerID = m.customerID AND h.isPrimary = "1" AND h.deletedAt IS NULL
                    JOIN companies AS c ON c.companyID = m.companyID
                    LEFT JOIN user as u ON u.userID = s.scheduledUserID
                    LEFT JOIN user as e ON e.userID = s.createdByUserID

                    WHERE s.projectScheduleID = :eventID AND c.companyID = :companyID AND s.deletedAt IS NULL LIMIT 1', [
                'eventID' => $eventID,
                'companyID' => $companyID
            ]))->first();
                break;
            case '2':
                $event = collect(DB::select('
                    SELECT s.userScheduleID, s.userID, s.scheduleType, s.scheduledStart, s.scheduledEnd, s.scheduledByUserID, 
                    u.userFirstName AS scheduledFirstName, u.userLastName AS scheduledLastName, e.userFirstName AS scheduledByFirstName, 
                    e.userLastName AS scheduledByLastName, s.scheduledOn
                                
                    FROM userSchedule AS s
    
                    JOIN user as u ON u.userID = s.userID
                    LEFT JOIN user as e ON e.userID = s.scheduledByUserID
                    JOIN companies AS c ON c.companyID = u.companyID
    
                    WHERE s.userScheduleID = :eventID AND c.companyID = :companyID LIMIT 1', [
                'eventID' => $eventID,
                'companyID' => $companyID
                ]))->first();
                break;
            case '3':
                $event = collect(DB::select('
                    SELECT s.companyScheduleID, s.companyID, s.scheduleType,
                    s.scheduledStart, s.scheduledEnd, s.scheduledByUserID,
                    u.userFirstName AS scheduledByFirstName, u.userLastName
                    AS scheduledByLastName, s.scheduledOn, c.name

                    FROM companySchedule AS s

                    LEFT JOIN user as u ON u.userID = s.scheduledByUserID
                    JOIN companies AS c ON c.companyID = s.companyID

                    WHERE s.companyScheduleID = :eventID AND c.companyID = :companyID LIMIT 1', [
                    'eventID' => $eventID,
                    'companyID' => $companyID
                ]))->first();
                break;
            default:
                throw new AppException('Event type is not valid');
        }


        if (!empty($event)) {

            $event->start = $event->scheduledStart;
            $event->end = $event->scheduledEnd;

            if ((isset($event->isAllDay) && $event->isAllDay === 1) || (date('H:i:s', strtotime($event->start)) == '00:00:00' && date('H:i:s', strtotime($event->end)) == '00:00:00')) {
                $event->start = date("F d, Y", strtotime($event->start));
                $event->end = date("F d, Y", strtotime($event->end));
            } else {
                $event->start = date("F d, Y h:i a", strtotime($event->start));
                $event->end = date("F d, Y h:i a", strtotime($event->end));
            }

            if ($event->scheduleType == 'Evaluation') {
                $event->scheduled = 'Salesperson: ' . $event->scheduledFirstName . ' ' . $event->scheduledLastName;
                unset($event->scheduledFirstName);
                unset($event->scheduledLastName);
            } else if ($event->scheduleType == 'Installation') {
                $event->scheduled = 'Installer: ' . $event->scheduledFirstName . ' ' . $event->scheduledLastName;
                unset($event->scheduledFirstName);
                unset($event->scheduledLastName);
            }

            if (isset($event->address)) {
                $directionsLink = GoogleMap::directionsUrl($event->address . ' ' . $event->city . ' ' . $event->state . ' ' . $event->zip);
                $event->directions = '<br/><a target="_blank" href="' . $directionsLink . '">Directions</a><br/>';
            } else {
                $event->directions = '';
            }

            if (!empty($event->email)) {
                $event->email = ' | <a href="mailto:' . $event->email . '">' . $event->email . '</a>';
            } else {
                $event->email = '';
            }

            $event->phoneNumber = isset($event->phoneNumber) ? '<a href="tel:' . $event->phoneNumber . '">' . $event->phoneNumber . '</a>' : '';

            $event->scheduledOn = $time_service->getFromUtc($event->scheduledOn)->format('F j, Y h:i a');
        }
    }

    echo json_encode($event);
?>