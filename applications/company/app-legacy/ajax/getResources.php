<?php

use App\Services\GoogleApi\Calendar\Classes\Calendar;

if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$companyName = $userArray['name'];
		$companyColor = $userArray['color'];

        $filter = NULL;
		
	if (isset($_GET['filter'])) {
        $filter = filter_input(INPUT_GET, 'filter', FILTER_DEFAULT, FILTER_REQUIRE_ARRAY);
        if (!empty($filter)) {
            $filter = array_filter($filter, function($value) {
                return is_numeric($value);
            });
        }
	}

	if ($filter == ['0']) {
		$companyResource = array(
                'id' => '0',
                'title' => $companyName,
                'installation' => '0',
                'sales' => '0',
                'calendarBgColor' => '#888888'
        );

		$resourcesArray[] = $companyResource;

		echo json_encode($resourcesArray);

	} else {

		include_once(__DIR__ . '/../includes/classes/class_Resources.php');

			$object = new Resources();
			$object->setCompany($companyID, $filter);
			$object->getResources();

			$resourcesArray = $object->getResults();

			if (!empty($resourcesArray)) {
				foreach ( $resourcesArray as $k=>$v ) {
				 	$resourcesArray[$k] ['id'] = $resourcesArray[$k] ['userID'];
				  	unset($resourcesArray[$k]['userID']);

					$resourcesArray[$k] ['title'] = $resourcesArray[$k] ['userFirstName'] . ' ' . $resourcesArray[$k] ['userLastName'];
				  	unset($resourcesArray[$k]['userFirstName']);
					unset($resourcesArray[$k]['userLastName']);

				}

				if(Calendar::existsForOwner(Calendar::OWNER_TYPE_COMPANY, (int) $companyID)) {
                    $companyResource = array(
                        'id' => '0',
                        'title' => $companyName,
                        'installation' => '0',
                        'sales' => '0',
                        'calendarBgColor' => '#888888'
                    );
                    array_unshift($resourcesArray, $companyResource);
                }

			}

			echo json_encode($resourcesArray);

	}

?>