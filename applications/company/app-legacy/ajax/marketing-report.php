<?php

use App\Classes\FX\Reports\MarketingReport;
use App\Services\TimeService;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Http\StaticAccessors\Input;
use Core\StaticAccessors\App;

/** @var TimeService $time_service */
$time_service = App::get(TimeService::class);

$dateFields = [
    'startDate' => 'Start Date',
    'endDate' => 'End Date'
];
$dates = [];
foreach ($dateFields as $key => $label) {
    $date = trim(Input::post($key, ''));
    if (strlen($date) === 0) {
        throw new Exception(sprintf('%s not specified', $label));
    }
    if (preg_match('#^(?<month>[0-9]{2})/(?<day>[0-9]{2})/(?<year>[0-9]{4})$#', $date, $match) !== 1) {
        throw new Exception(sprintf('%s is not valid', $label));
    }
    $dates[$key] = "{$match['year']}-{$match['month']}-{$match['day']}";
}

$start_date = $time_service->get($dates['startDate'])->startOfDay();
$end_date = $time_service->get($dates['endDate'])->endOfDay();

$companyID = Auth::user()->companyID;

	function calculateSourceData($source, $leadsAll, $totalMarketingCostsAll, $isSubsource){
		$subsources = NULL;
		$sourceLeads = $source['leads'];
		$sourceAppointments = $source['appointments'];
		$sourceBids = $source['bids'];
		$sourceSales = $source['sales'];
		$sourceGrossSales = $source['grossSales'];
		$sourceMarketingCosts = $source['spendAmount'];
		$unspecified = $source['unspecified'];
		if (!$isSubsource){
			$subsources = $source['subsources'];
		}

		if ($unspecified){
			$sourceMarketingCosts = '0';
		}

		//Percent Total Marketing Costs = sourceMarketingCosts/totalMarketingCostsAll
		$percentTotalMarketingCosts = '0.00%';
		if (!empty($sourceMarketingCosts) && !(empty($totalMarketingCostsAll)) && !$unspecified){
			$percentTotalMarketingCosts = $sourceMarketingCosts/$totalMarketingCostsAll;
			$percentTotalMarketingCosts = sprintf("%.2f%%", $percentTotalMarketingCosts * 100); //format to two decimal places
		}

		//Percent Total Leads = sourceLeads/leadsAll
		$percentTotalLeads = '0.00%';
		if (!empty($sourceLeads) && !(empty($leadsAll))){
			$percentTotalLeads = $sourceLeads/$leadsAll;
			$percentTotalLeads = sprintf("%.2f%%", $percentTotalLeads * 100); //format to two decimal places
		}

		//Cost per lead = sourceMarketingCosts/sourceLeads
		$costPerLead = '0';
		if (!empty($sourceLeads) && !(empty($sourceMarketingCosts))){
			$costPerLead = $sourceMarketingCosts/$sourceLeads;
		}

		//Cost Per Sale = sourceMarketingCosts/sourceSales
		$costPerSale = '0';
		if (!empty($sourceSales) && !(empty($sourceMarketingCosts))){
			$costPerSale = $sourceMarketingCosts/$sourceSales;
		}
		
		//Revenue Per Lead = sourceGrossSales/sourceLeads
		$revenuePerLead = '0';
		if (!empty($sourceLeads) && !(empty($sourceGrossSales))){
			$revenuePerLead = $sourceGrossSales/$sourceLeads;
		}

		$marketingMetricsArraySource = array(
									'leadSource' => $source['marketingTypeName'],
									'totalMarketingCosts' => '$' . number_format($sourceMarketingCosts, 2, '.', ','),
									'grossSales' => '$' . number_format($sourceGrossSales, 2, '.', ','),
									'leads' => $sourceLeads,
									'appointments' => $sourceAppointments,
									'bids' => $sourceBids,
									'sales' => $sourceSales,
									'costPerLead' => '$' . number_format($costPerLead, 2, '.', ','),
									'costPerSale' => '$' . number_format($costPerSale, 2, '.', ','),
									'percentTotalMarketingCosts' => $percentTotalMarketingCosts,
									'percentTotalLeads' => $percentTotalLeads,
									'revenuePerLead' => '$' . number_format($revenuePerLead, 2, '.', ','),
									'subsources' => $subsources, 'unspecified' => $unspecified);
		return $marketingMetricsArraySource;
	}

	$object = new MarketingReport();
	$all_sources = $object->setCompanyID($companyID)
        ->setStartDate($time_service->getUtc($start_date))
        ->setEndDate($time_service->getUtc($end_date))
        ->run();

		$marketingMetricsArray = array();

		//Get Metrics for All Marketing
        $marketingAll = $all_sources;

		$totalMarketingCostsAll = $marketingAll['totalMarketingCosts'];
		$leadsAll = $marketingAll['leads'];
		$grossSalesAll = $marketingAll['grossSales'];
		$appointmentsAll = $marketingAll['appointments'];
		$bidsAll = $marketingAll['bids'];
		$salesAll = $marketingAll['sales'];
		$unspecified = false;

		//Cost per lead = totalMarketingCosts/leadsAll
		$costPerLeadAll = '0';
		if (!empty($leadsAll) && !(empty($totalMarketingCostsAll))){
			$costPerLeadAll = $totalMarketingCostsAll/$leadsAll;
		}

		//Cost Per Sale = totalMarketingCostsAll/salesAll
		$costPerSaleAll = '0';
		if (!empty($salesAll) && !(empty($totalMarketingCostsAll))){
			$costPerSaleAll = $totalMarketingCostsAll/$salesAll;
		}
		
		//Revenue Per Lead = grossSalesAll/leadsAll
		$revenuePerLeadAll = '0';
		if (!empty($leadsAll) && !(empty($grossSalesAll))){
			$revenuePerLeadAll = $grossSalesAll/$leadsAll;
		}

		$marketingMetricsArrayAll = array(
										'leadSource' => 'All Marketing',
										'totalMarketingCosts' => '$' . number_format($totalMarketingCostsAll, 2, '.', ','),
										'grossSales' => '$' . number_format($grossSalesAll, 2, '.', ','),
										'leads' => $leadsAll,
										'appointments' => $appointmentsAll,
										'bids' => $bidsAll,
										'sales' => $salesAll,
										'costPerLead' => '$' . number_format($costPerLeadAll, 2, '.', ','),
										'costPerSale' => '$' . number_format($costPerSaleAll, 2, '.', ','),
										'percentTotalMarketingCosts' => '100.00%',
										'percentTotalLeads' => '100.00%',
										'revenuePerLead' => '$' . number_format($revenuePerLeadAll, 2, '.', ','), 'unspecified' => $unspecified);

		$marketingMetricsArray['all'] = $marketingMetricsArrayAll;

		$unsourcedArray = array();

		//Get Metrics for Projects With No Source
        $unsourced = $all_sources['subsources'][null];
        unset($all_sources['subsources'][null]);

		$totalMarketingCostsUnsourced = '$0.00';
		$costPerLeadUnsourced = '$0.00';
		$costPerSaleUnsourced = '$0.00';
		$leadsUnsourced = $unsourced['leads'];
		$grossSalesUnsourced = $unsourced['grossSales'];
		$appointmentsUnsourced = $unsourced['appointments'];
		$bidsUnsourced = $unsourced['bids'];
		$salesUnsourced = $unsourced['sales'];
		$unspecified = false;

		//Percent Total Leads = sourceLeads/leadsAll
		$percentTotalLeadsUnsourced = '0.00%';
		if (!empty($leadsUnsourced) && !(empty($leadsAll))){
			$percentTotalLeadsUnsourced = $leadsUnsourced/$leadsAll;
			$percentTotalLeadsUnsourced = sprintf("%.2f%%", $percentTotalLeadsUnsourced * 100); //format to two decimal places
		}

		//Revenue Per Lead = grossSalesUnsourced/leadsUnsourced
		$revenuePerLeadUnsourced = '0';
		if (!empty($leadsUnsourced) && !(empty($grossSalesUnsourced))){
			$revenuePerLeadUnsourced = $grossSalesUnsourced/$leadsUnsourced;
		}

		$unsourcedArray = array(
										'leadSource' => 'Uncategorized',
										'totalMarketingCosts' => $totalMarketingCostsUnsourced,
										'grossSales' => '$' . number_format($grossSalesUnsourced, 2, '.', ','),
										'leads' => $leadsUnsourced,
										'appointments' => $appointmentsUnsourced,
										'bids' => $bidsUnsourced,
										'sales' => $salesUnsourced,
										'costPerLead' => $costPerSaleUnsourced,
										'costPerSale' => $costPerSaleUnsourced,
										'percentTotalMarketingCosts' => '0.00%',
										'percentTotalLeads' => $percentTotalLeadsUnsourced,
										'revenuePerLead' => '$' . number_format($revenuePerLeadUnsourced, 2, '.', ','), 'unspecified' => $unspecified);

		$marketingMetricsArray['unsourced'] = $unsourcedArray;

		//Get Metrics for Sources
        $sources = $all_sources['subsources'];

		foreach ($sources as $source) {
			$marketingMetricsArraySource = calculateSourceData($source, $leadsAll, $totalMarketingCostsAll, false);
			$subsources = $marketingMetricsArraySource['subsources'];
			$marketingMetricsArraySource['subsources'] = NULL;
			$marketingMetricsArraySubsource = NULL;

			if(!empty($subsources)){
				foreach ($subsources as $subsource) {
					$marketingMetricsArraySubsource[] = calculateSourceData($subsource, $leadsAll, $totalMarketingCostsAll, true);

				}
				$marketingMetricsArraySource['subsources'] = $marketingMetricsArraySubsource;
			}

			$marketingMetricsArray['sources'][] = $marketingMetricsArraySource;
		}
		
		echo json_encode($marketingMetricsArray);
?>