<?php

use App\NotificationJobs\Evaluation\ViewNotificationJob;
use App\ResourceJobs\Bid\Item\GenerateJob;
use Common\Models\BidItem;
use Common\Models\Evaluation;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\DB\StaticAccessors\DB;
use Core\Components\Http\StaticAccessors\Response;
use Ramsey\Uuid\Uuid;

try {
    if (!isset($_GET['idToResend'])) {
        throw new Exception('No id defined');
    }
    $evaluation = Evaluation::query()
        ->select(['evaluationID', DB::raw('IF(evaluation.bidItemID IS NOT NULL AND bidItems.type = ' . BidItem::TYPE_LEGACY . ', NULL, evaluation.bidItemID) as bidItemID')])
        ->leftJoin('bidItems', 'bidItems.bidItemID', '=', 'evaluation.bidItemID')
        ->ofCompany(Auth::user()->companyID)
        ->whereKey($_GET['idToResend'])
        ->first();
    if ($evaluation === null) {
        throw new Exception('Unable to find evaluation');
    }
    if ($evaluation->bidItemID !== null) {
        GenerateJob::enqueue(Uuid::fromBytes($evaluation->bidItemID));
    } else {
        ViewNotificationJob::enqueue($evaluation->getKey());
    }
    return Response::json(['success' => true]);
} catch (Exception $e) {
    return Response::json(['success' => false, 'error' => $e->getmessage()]);
}
