<?php

use Core\Components\Http\StaticAccessors\URI;
use Ramsey\Uuid\Uuid;

function makeLeadLink($id) {
        return  URI::route('page.app.leads', [
            'path' => '/details/'.$id.'/'
        ])->build();
    };

    function makeProjectLink($id, $page) {
        return  URI::route('page.app.projects', [
            'path' => '/'.$id.'/'.$page
        ])->build();
    };

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 
	
	//else {
		//header('location:login.php');
	//}


	include_once(__DIR__ . '/../includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$marketing = $userArray['marketing'];
		$bidCreation = $userArray['bidCreation'];
		$bidVerification = $userArray['bidVerification'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$timecardApprover = $userArray['timecardApprover'];
		$recentlyCompleted = $userArray['recentlyCompletedStatus'];
		

		if(isset($_GET['sort'])) {
			$sort = filter_input(INPUT_GET, 'sort', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
		}
        $groupBy = isset($_GET['groupBy']) && $_GET['groupBy'] === 'true';
	
	$projectStatusArray = [
	    'results' => []
    ];
	

	//TO DO: FILTER ALL QUERIES BY ROLE - ONLY RUN IF THEY HAVE SPECIFIC ROLES
    if ($primary == 1 || $projectManagement == 1 || $sales == 1) {

        //Get Status for Leads That Have Been Created and Are Pending a Conversion
        include_once(__DIR__ . '/../includes/classes/class_PS_LeadCreated.php');

        $object = new LeadCreated();
        $object->setStatus($companyID, $sort, $groupBy);

        if ($primary == 1 || $projectManagement == 1) {
            $object->getStatus();
        } else {
            $object->setUser($userID);
            $object->getStatusUser();
        }

        $leadCreatedArray = $object->getResults();

        if (!empty($leadCreatedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $leadCreatedArray);

        $projectStatusArray['LeadCreatedCount'] = is_array($leadCreatedArray) ? count($leadCreatedArray) : 0;
    }

	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {

		//Get Status for Projects That Have Been Created and Are Pending a Sales Appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_ProjectCreated.php');
				
			$object = new ProjectCreated();
			$object->setStatus($companyID, $sort, $groupBy);
			
			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
		
			$projectCreatedArray = $object->getResults();	
			
			if (!empty($projectCreatedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $projectCreatedArray);

			$projectStatusArray['ProjectCreatedCount'] = is_array($projectCreatedArray) ? count($projectCreatedArray) : 0;
	}


//Once Evaluation is Created then all sales projects/evaluations need to be filtered by who did the evaluation

	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Appointments That Have Been Scheduled

		//Assigned to that salesman or salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_AppointmentScheduled.php');
				
			$object = new AppointmentScheduled();
			$object->setStatus($companyID, $sort, $groupBy);

			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$appointmentScheduledArray = $object->getResults();	
			
			if (!empty($appointmentScheduledArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $appointmentScheduledArray);

			$projectStatusArray['AppointmentScheduledCount'] = is_array($appointmentScheduledArray) ? count($appointmentScheduledArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Appointments Completed But Evaluation Hasn't Started

		//Assigned to that salesman or salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_AppointmentCompleted.php');
				
			$object = new AppointmentCompleted();
			$object->setStatus($companyID, $sort, $groupBy);

			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$appointmentCompletedArray = $object->getResults();	
			
			if (!empty($appointmentCompletedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $appointmentCompletedArray);

			$projectStatusArray['AppointmentCompletedCount'] = is_array($appointmentCompletedArray) ? count($appointmentCompletedArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1 || $bidCreation == 1) {
		//Get Status for Repair Plans That Have Been Created And Need to be Turned Into a Bid

		//Salesman that is assigned to project or Salesman that did the evaluation

		include_once(__DIR__ . '/../includes/classes/class_PS_RepairPlanCreated.php');
				
			$object = new RepairPlanCreated();
			$object->setStatus($companyID, $sort, $groupBy);

        if ($primary == 1 || $projectManagement == 1 || ($bidCreation == 1 && $sales != 1)) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$repairPlanCreatedArray = $object->getResults();	
			
			if (!empty($repairPlanCreatedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $repairPlanCreatedArray);

			$projectStatusArray['RepairPlanCreatedCount'] = is_array($repairPlanCreatedArray) ? count($repairPlanCreatedArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1 || $bidVerification == 1 || $bidCreation == 1) {
		//Get Status for Bids that Have Been Created And Need to be Sent (or approved by bid approver)

		//Salesman that is assigned to project
		//Salesman that did the evaluation 

		include_once(__DIR__ . '/../includes/classes/class_PS_BidCreated.php');
				
			$object = new BidCreated();
			$object->setStatus($companyID, $sort, $groupBy);

            if ($primary == 1 || $projectManagement == 1 || ($bidVerification == 1 && $sales != 1) || ($bidCreation == 1 && $sales != 1)) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$bidCreatedArray = $object->getResults();	
			
			if (!empty($bidCreatedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $bidCreatedArray);

			$projectStatusArray['BidCreatedCount'] = is_array($bidCreatedArray) ? count($bidCreatedArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Bids That Have Been Sent And Need to be Approved/Rejected
		include_once(__DIR__ . '/../includes/classes/class_PS_BidSent.php');
				
			$object = new BidSent();
			$object->setStatus($companyID, $sort, $groupBy);
			
			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$bidSentArray = $object->getResults();	
			
			if (!empty($bidSentArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $bidSentArray);

			$projectStatusArray['BidSentCount'] = is_array($bidSentArray) ? count($bidSentArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $installation == 1 || $sales == 1) {
		//Get Status for Bids That Have Been Accepted And Need an Installation Appointment 
		include_once(__DIR__ . '/../includes/classes/class_PS_BidAccepted.php');

		//Salesman that is assigned to project
		//Salesman that did the evaluation
				
			$object = new BidAccepted();
			$object->setStatus($companyID, $sort, $groupBy);
			
			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$bidAcceptedArray = $object->getResults();	
			
			if (!empty($bidAcceptedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $bidAcceptedArray);

			$projectStatusArray['BidAcceptedCount'] = is_array($bidAcceptedArray) ? count($bidAcceptedArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1 || $bidCreation == 1 || $bidVerification == 1) {
		//Get Status for Bids That Have Been Rejected And Need To Be Cancelled or Rebid

		//Salesman that is assigned to project
		//Salesman that did the evaluation 
		include_once(__DIR__ . '/../includes/classes/class_PS_BidRejected.php');
				
			$object = new BidRejected();
			$object->setStatus($companyID, $sort, $groupBy);

            if ($primary == 1 || $projectManagement == 1 || ($bidCreation == 1 && $sales != 1) || ($bidVerification == 1 && $sales != 1)) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$bidRejectedArray = $object->getResults();	
			
			if (!empty($bidRejectedArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $bidRejectedArray);

			$projectStatusArray['BidRejectedCount'] = is_array($bidRejectedArray) ? count($bidRejectedArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $installation == 1 || $sales == 1) {
		//Get Status for Installations That Are Scheduled and Need to be Marked Complete

		//Salesman that is assigned to project
		//Salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_InstallationScheduled.php');
				
			$object = new InstallationScheduled();
			$object->setStatus($companyID, $sort, $groupBy);

			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else if ($installation == 1) {
				$object->setUser($userID);
				$object->getStatusInstaller();
			} else {
				$object->setUser($userID);
				$object->getStatusSales();
			}
			
			$installationScheduledArray = $object->getResults();	
			
			if (!empty($installationScheduledArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $installationScheduledArray);

			$projectStatusArray['InstallationScheduledCount'] = is_array($installationScheduledArray) ? count($installationScheduledArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Installations That Are Complete and Need Final Report Sent

		//Salesman that is assigned to project
		//Salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_InstallationComplete.php');
				
			$object = new InstallationComplete();
			$object->setStatus($companyID, $sort, $groupBy);

			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$installationCompleteArray = $object->getResults();	
			
			if (!empty($installationCompleteArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $installationCompleteArray);

			$projectStatusArray['InstallationCompleteCount'] = is_array($installationCompleteArray) ? count($installationCompleteArray) : 0;
	}


	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Final Report Sent And Project Needs To Be Marked Complete

		//Salesman that is assigned to project
		//Salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_FinalReportSent.php');
				
			$object = new FinalReportSent();
			$object->setStatus($companyID, $sort, $groupBy);

			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$finalReportSentArray = $object->getResults();	
			
			if (!empty($finalReportSentArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $finalReportSentArray);

			$projectStatusArray['FinalReportSentCount'] = is_array($finalReportSentArray) ? count($finalReportSentArray) : 0;
	}

	if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
		//Get Status for Recently Closed Projects

		//Salesman that is assigned to project
		//Salesman that did the evaluation appointment
		include_once(__DIR__ . '/../includes/classes/class_PS_CompletedProject.php');
				
			$object = new CompletedProject();
			$object->setStatus($companyID, $sort, $recentlyCompleted, $groupBy);
			
			if ($primary == 1 || $projectManagement == 1) {
				$object->getStatus();
			} else {
				$object->setUser($userID);
				$object->getStatusUser();
			}
			
			$CompletedProjectArray = $object->getResults();	
			
			if (!empty($CompletedProjectArray)) $projectStatusArray['results'] = array_merge($projectStatusArray['results'], $CompletedProjectArray);

			$projectStatusArray['CompletedProjectCount'] = is_array($CompletedProjectArray) ? count($CompletedProjectArray) : 0;
	}

	
	//Filter Status Type
	if ($projectStatusArray != '') {
		
		foreach($projectStatusArray['results'] as $k => $v)
		{
			
			//$todaysDate = date('Y-m-d g:i a'); 
			//Get UTC Time
			$todaysDateGMDate = gmdate("Y-m-d H:i:s");
			$todaysDateGMDate = new DateTime();

			$todaysDateTime = date("Y-m-d H:i:s");
			$todaysDateTime = new DateTime();

			$todaysDate = date("Y-m-d");

            $status = '';

            if ($projectStatusArray['results'][$k]['statusType'] == 'LeadCreated') {
                $projectStatusArray['results'][$k]['statusType'] = '0';

                $projectStatusArray['results'][$k]['link'] = makeLeadLink(Uuid::fromBytes($projectStatusArray['results'][$k]['link'])->toString());
                $time = new DateTime($projectStatusArray['results'][$k] ['time']);
                $daysAgo = $todaysDateGMDate->diff($time);
                $daysAgo = $daysAgo->format("%a");
                $projectStatusArray['results'][$k]['time'] = 'Lead Created '.$daysAgo.' Days Ago';
                $projectStatusArray['results'][$k]['sort'] = $daysAgo;
                $status = $projectStatusArray['results'][$k] ['status'];

                switch($status) {
                    case 1:
                        $status = 'Status: New';
                        break;
                    case 2:
                        $status = 'Status: Working';
                        break;
                    case 3:
                        $status = 'Status: Converted';
                        break;
                    case 4:
                        $status = 'Status: Dead';
                        break;
                }
                $projectStatusArray['results'][$k]['addressDisplay'] = $status;
            } else if ($projectStatusArray['results'][$k]['statusType'] == 'ProjectCreated') {
				$projectStatusArray['results'][$k]['statusType'] = '1';

				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'schedule');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateGMDate->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Project Created '.$daysAgo.' Days Ago'; 
				$projectStatusArray['results'][$k]['sort'] = $daysAgo; 

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'AppointmentScheduled') {
				$projectStatusArray['results'][$k]['statusType'] = '2';
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'schedule');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgoDisplay = $daysAgo->format("%a"); 
				$daysAgo = $daysAgo->format("%R%a"); 
				if ($daysAgo > 0) {
					if ($daysAgo == 1) {
						$projectStatusArray['results'][$k]['time'] = 'Appointment is in '.$daysAgoDisplay.' Day'; 
					} else {
						$projectStatusArray['results'][$k]['time'] = 'Appointment is in '.$daysAgoDisplay.' Days'; 
					}
					$projectStatusArray['results'][$k]['sort'] = $daysAgo; 
				} else if ($daysAgo == 0) {
					$thisTime = date('Y-m-d', strtotime($projectStatusArray['results'][$k] ['time'])); 

					if ($todaysDate == $thisTime) {
						$projectStatusArray['results'][$k]['time'] = 'Appointment is today'; 
					} else {
						$projectStatusArray['results'][$k]['time'] = 'Appointment is tomorrow'; 
					}
					$projectStatusArray['results'][$k]['sort'] = $daysAgo; 
				}

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
				
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'AppointmentCompleted') {
				$projectStatusArray['results'][$k]['statusType'] = '3'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'bids');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Appointment Completed '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'RepairPlanCreated') {
				$projectStatusArray['results'][$k]['statusType'] = '4'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'bids');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Evaluation Created '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'BidCreated') {
				$projectStatusArray['results'][$k]['statusType'] = '5';
                $time = new DateTime($projectStatusArray['results'][$k] ['time']);
                $daysAgo = $todaysDateTime->diff($time);
                $daysAgo = $daysAgo->format("%a");
                $projectStatusArray['results'][$k]['sort'] = $daysAgo;

                if ($projectStatusArray['results'][$k]['customBid'] == 'true') {
					$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'bids');
                    $projectStatusArray['results'][$k]['time'] = 'Bid Submitted For Approval '.$daysAgo.' Days Ago';
				}

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'BidSent') {
				$projectStatusArray['results'][$k]['statusType'] = '6'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], '');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a");
                $projectStatusArray['results'][$k]['time'] = 'Bid Sent '.$daysAgo.' Days Ago';
                if ($projectStatusArray['results'][$k]['bidLastViewed'] === null) {
                    $projectStatusArray['results'][$k]['time'] .= ' <strong>(not viewed)</strong>';
                }
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'BidAccepted') {
				$projectStatusArray['results'][$k]['statusType'] = '7'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'schedule');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Bid Accepted '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'BidRejected') {
				$projectStatusArray['results'][$k]['statusType'] = '8'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'bids');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Bid Rejected '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'InstallationScheduled') {
				$projectStatusArray['results'][$k]['statusType'] = '9'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'schedule');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgoDisplay = $daysAgo->format("%a"); 
				$daysAgo = $daysAgo->format("%R%a"); 
				if ($daysAgo > 0) {
					if ($daysAgo == 1) {
						$projectStatusArray['results'][$k]['time'] = 'Installation is in '.$daysAgoDisplay.' Day'; 
					} else {
						$projectStatusArray['results'][$k]['time'] = 'Installation is in '.$daysAgoDisplay.' Days'; 
					}
					$projectStatusArray['results'][$k]['sort'] = $daysAgo; 
				} else if ($daysAgo == 0) {
					$thisTime = date('Y-m-d', strtotime($projectStatusArray['results'][$k] ['time'])); 

					if ($todaysDate == $thisTime) {
						$projectStatusArray['results'][$k]['time'] = 'Installation is today'; 
					} else {
						$projectStatusArray['results'][$k]['time'] = 'Installation is tomorrow'; 
					}
					$projectStatusArray['results'][$k]['sort'] = $daysAgo; 
				} else if ($daysAgo < 0) {
					if ($daysAgo == -1) {
						$projectStatusArray['results'][$k]['time'] = 'Installation started '.$daysAgoDisplay.' Day Ago'; 
					} else {
						$projectStatusArray['results'][$k]['time'] = 'Installation started '.$daysAgoDisplay.' Days Ago'; 
					}
					$projectStatusArray['results'][$k]['sort'] = $daysAgo; 
				}

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'InstallationComplete') {
				$projectStatusArray['results'][$k]['statusType'] = '10'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], 'bids');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Installation Completed '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'FinalReportSent') {
				$projectStatusArray['results'][$k]['statusType'] = '11'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], '');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Warranties Sent '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			else if ($projectStatusArray['results'][$k]['statusType'] == 'CompletedProject') {
				$projectStatusArray['results'][$k]['statusType'] = '12'; 
				$projectStatusArray['results'][$k]['link'] = makeProjectLink($projectStatusArray['results'][$k]['link'], '');
				$time = new DateTime($projectStatusArray['results'][$k] ['time']);
				$daysAgo = $todaysDateTime->diff($time);
				$daysAgo = $daysAgo->format("%a"); 
				$projectStatusArray['results'][$k]['time'] = 'Completed '.$daysAgo.' Days Ago';
				$projectStatusArray['results'][$k]['sort'] = $daysAgo;  

				if ($projectStatusArray['results'][$k]['address2'] == '') {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				} else {
					$projectStatusArray['results'][$k]['addressDisplay'] = $projectStatusArray['results'][$k]['address'] . ', ' . $projectStatusArray['results'][$k]['address2'] . ', ' . $projectStatusArray['results'][$k]['city'] . ', ' . $projectStatusArray['results'][$k]['state'];
				}
			}
			
		
		}
			
	} 

	//SORT_DESC
	
	// function array_sort_by_column(&$arr, $col, $dir = SORT_DESC) {
 //    $sort_col = array();
 //    foreach ($arr as $key=> $row) {
 //        $sort_col[$key] = $row[$col];
 //    }

 //    	array_multisort($sort_col, $dir, $arr);
	// }


	//array_sort_by_column($projectStatusArray, 'sort');
	

	echo json_encode($projectStatusArray);			
?>