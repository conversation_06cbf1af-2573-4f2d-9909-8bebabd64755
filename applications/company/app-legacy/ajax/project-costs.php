<?php

use Common\Models\Project;
use Common\Models\ProjectCommissionCost;
use Common\Models\ProjectCost;
use Common\Models\ProjectCostType;
use Common\Models\ProjectGeneralCost;
use Common\Models\ProjectPaymentType;
use Common\Models\User;
use Carbon\Carbon;
use Core\Exceptions\AppException;

$user = Auth::user();


$json = [
    'status' => 1,
    'result' => null
];
try {
    //Logged In User
    $user = Auth::user();

    //Check if this logged in user can view and modify project costs
    $roles = [
        'viewCosts' => [
            'primary', 'projectManagement', 'sales', 'installation'
        ],
        'editCostsGeneral' => [
            'primary', 'projectManagement',
        ],
        'editCostsCommission' => [
            'primary', 'projectManagement'
        ],
        'editCostTypes' => [
            'primary', 'projectManagement'
        ],
        'deleteCosts' => [
            'primary', 'projectManagement'
        ]
    ];

    $costPermissions = [
        'sales' => [
            ProjectCost::TYPE_GENERAL
        ]
    ];

    $settings = [
        'commissions' => true,
        'editCostTypes' => true,
        'editCosts' => true
    ];

    function checkRoles($user, $array, $exception = null) {
        $allow = false;
        foreach ($array as $role) {
            if ($user[$role] == 1) {
                $allow = true;
                break;
            }
        }
        if ($exception === null){
            return $allow;
        } else if (!$allow) {
            throw new AppException($exception);
        }
    }

    $now = Carbon::now('UTC');

    //Action
    if (($action = Input::post('action')) === null) {
        throw new AppException('No action defined');
    }

    //projectID
    if (($projectID = Input::post('projectID')) === null) {
        throw new AppException('No project ID defined');
    }
    if (!is_numeric($projectID)) {
        throw new AppException('Project ID is not numeric');
    }
    if (($project = Project::find($projectID)) === null) {
        throw new AppException('Unable to find project item');
    }
    if ($project->property->customer->companyID !== $user->companyID) {
        throw new AppException('Project item not assigned to proper company');
    }

    switch ($action) {
        case 'costs':
            //User Role Permissions
            checkRoles($user, $roles['viewCosts'], 'User does not have proper role to see project costs.');

            $projectCosts = $project->costs()->with(['costType' => function ($query) {
                $query->withTrashed();
            }]);

            $settingsAllow = checkRoles($user, $roles['editCostsCommission']);

            if (!$settingsAllow) {
                $projectCosts->whereIn('type', $costPermissions['sales']);
            }

            $projectCosts = $projectCosts->orderBy('incurredAt', 'ASC')->get()->keyBy('projectCostID');

            $costTypes = [];

            foreach ($projectCosts as $cost) {
                if (!isset($costTypes[$cost->type])) {
                    $costTypes[$cost->type] = [];
                }
                $costTypes[$cost->type][$cost->costID] = $cost->projectCostID;
            }

            $typeModels = [
                ProjectCost::TYPE_GENERAL => [
                    'model' => ProjectGeneralCost::class
                ],
                ProjectCost::TYPE_COMMISSION => [
                    'model' => ProjectCommissionCost::class,
                    'query' => function ($query) {
                        $query->with(['user' => function ($query) {
                            $query->select('userID', 'userFirstName as firstName', 'userLastName as lastName');
                        }]);
                    }
                ]
            ];

            foreach ($costTypes as $type => $ids) {
                $config = $typeModels[$type];
                $model = $config['model'];
                $query = $model::query();
                if (isset($config['query'])) {
                    $config['query']($query);
                }
                $query->whereIn($query->getModel()->getKeyName(), array_keys($ids))->get()->each(function ($model) use ($projectCosts, $ids) {
                    $projectCosts[$ids[$model->getKey()]]->cost = $model;
                });
            }


            $punchTimes = [];
            if ($settingsAllow) {
                $punchTimes = $project->punchTimes()->select('crewman.firstName', 'crewman.lastName',
                    DB::raw('SUM(CAST(TIME_TO_SEC(TIMEDIFF(outTime, inTime)) / 3600 as decimal(10, 1))) AS hours'),
                    DB::raw('SUM(CAST((CAST(TIME_TO_SEC(TIMEDIFF(outTime, inTime)) / 3600 AS DECIMAL(10, 1)) * crewman.hourlyPayRate) AS DECIMAL(10, 2))) AS total'))
                    ->join('crewman', 'punchTime.crewmanID', '=', 'crewman.crewmanID')
                    ->groupBy('crewman.crewmanID')
                    ->whereNotNull('hourlyPayRate')->get()->toArray();
            }

            $project_costs = $projectCosts->toArray();
            foreach ($project_costs as &$cost) {
                $cost['incurredAt'] = Carbon::parse($cost['incurredAt'])->format('Y-m-d');
            }

            //Get All Project Costs
            $json['result'] = [
                'projectCosts' => $project_costs,
                'crewmanCosts' => $punchTimes
            ];

            break;
        case 'costSettings':
            //User Role Permissions
            checkRoles($user, $roles['viewCosts'], 'User does not have proper role to see project cost settings.');

            $projectCostTypes = $user->company->projectCostTypes()->orderBy('title', 'asc');
            $projectCostCategories = $user->company->projectCostCategories()->orderBy('name', 'asc')->get();

            $settingsAllow = checkRoles($user, $roles['editCostsCommission']);

            if (!$settingsAllow) {
                $projectCostTypes->whereIn('type', $costPermissions['sales']);
                $settings['commissions'] = false;
                $settings['editCostTypes'] = false;
                $settings['editCosts'] = false;
            }

            $projectCostTypes = $projectCostTypes->get();

            //Get All Project Cost Types, Payment Types, and Active Sales/Installation Users
            $json['result'] = [
                'settings' => $settings,
                'costCategories' => $projectCostCategories,
                'costTypes' => $projectCostTypes,
                'paymentTypes' => ProjectPaymentType::all(),
                'users' => $user->company->users()->active()->notInvited()
                    ->anyRoles([User::ROLE_SALES, User::ROLE_INSTALLATION])
                    ->get(['userID','userFirstName','userLastName', 'sales', 'installation'])
            ];

            break;
        case 'editProjectCostTypes':
            //User Role Permissions
            checkRoles($user, $roles['editCostTypes'], 'User does not have proper role to edit project cost types.');

            //Edit Project Cost Types
            $projectCostTypes = Input::post('projectCostTypes');
            $existingCostTypes = $user->company->projectCostTypes->keyBy('projectCostTypeID');
            $now = Carbon::now('UTC');

            if (!is_array($projectCostTypes)){
                throw new AppException('Project Cost Types are not correct.');
            }

            $projectCostTypesID = collect($projectCostTypes)->pluck('projectCostTypeID')->toArray();
            $existingCostTypesID = $existingCostTypes->keys()->toArray();

            $results = array_diff($existingCostTypesID, $projectCostTypesID);

            foreach ($results as $result){
                if ($existingCostTypes[$result]->canDelete){
                    //@TODO Change this back to soft delete system once timezone is corrected
                    $existingCostTypes[$result]->deletedAt = $now;
                    $existingCostTypes[$result]->deletedByUserID = $user->userID;
                    $existingCostTypes[$result]->save();
                }
            }

            foreach ($projectCostTypes as $types){
                $projectCostTypeID = $types['projectCostTypeID'];
                $title = $types['title'];
                $projectCostCategoryID = (int) $types['projectCostCategoryID'];

                if ($projectCostTypeID !== ''){
                    if (isset($existingCostTypes[$projectCostTypeID])){
                        if ($existingCostTypes[$projectCostTypeID]->title !== $title || $existingCostTypes[$projectCostTypeID]->projectCostCategoryID !== $projectCostCategoryID) {
                            $existingCostTypes[$projectCostTypeID]->title = $title;
                            $existingCostTypes[$projectCostTypeID]->projectCostCategoryID = $projectCostCategoryID;
                            $existingCostTypes[$projectCostTypeID]->updatedAt = $now;
                            $existingCostTypes[$projectCostTypeID]->updatedByUserID = $user->userID;
                            $existingCostTypes[$projectCostTypeID]->save();
                        }
                    }
                } else {
                    ProjectCostType::create([
                        'companyID' => $user->company->companyID,
                        'type' => ProjectCostType::TYPE_GENERAL,
                        'projectCostCategoryID' => $projectCostCategoryID,
                        'title' => $title,
                        'canDelete' => true,
                        'createdAt' => $now,
                        'createdByUserID' => $user->userID,
                        'updatedAt' => $now,
                        'updatedByUserID' => $user->userID
                    ]);
                }
            }

            break;
        case 'saveProjectCost':

            $projectCostItem = Input::post('projectCostItem');

            if (!is_array($projectCostItem)){
                throw new AppException('Project Cost is not correct.');
            }

            $costAction = $projectCostItem['costAction'];

            if ($costAction === 'edit') {
                //projectCostID
                if (!isset($projectCostItem['projectCostID'])) {
                    throw new AppException('No project cost ID defined');
                }
                $projectCostID = $projectCostItem['projectCostID'];

                if (!is_numeric($projectCostID)) {
                    throw new AppException('Project cost ID is not numeric');
                }
                if (($projectCost = ProjectCost::find($projectCostID)) === null) {
                    throw new AppException('Unable to find project cost item');
                }

                //ProjectCost Item
                if ($projectCost->project->property->customer->companyID !== $user->companyID) {
                    throw new AppException('Project cost item not assigned to proper company');
                }

                //projectID
                if ($projectCost->projectID != $projectID) {
                    throw new AppException('Project ID does not match this project cost item project ID');
                }

                //costID
                if (!isset($projectCostItem['costID'])) {
                    throw new AppException('No project cost ID defined');
                }
                if (!is_numeric($projectCostItem['costID'])) {
                    throw new AppException('Project cost ID is not numeric');
                }
                if ($projectCost->costID != $projectCostItem['costID']) {
                    throw new AppException('Cost ID does not match the project cost item.');
                }
            } else if ($costAction === 'add') {
                //projectCostTypeID
                if (!isset($projectCostItem['projectCostTypeID'])) {
                    throw new AppException('No project cost type ID defined');
                }
                if (!is_numeric($projectCostItem['projectCostTypeID'])) {
                    throw new AppException('Project cost type ID is not numeric');
                }
                if (($projectCostTypeItem = ProjectCostType::find($projectCostItem['projectCostTypeID'])) === null) {
                    throw new AppException('Unable to find project cost type');
                }
            }

            //incurredAt
            if (preg_match('#^(?<month>[0-9]{2})/(?<day>[0-9]{2})/(?<year>[0-9]{4})$#', $projectCostItem['incurredAt'], $match) !== 1) {
                throw new AppException($projectCostItem['incurredAt'] . ' is not a valid format');
            }
            try {
                $projectCostItem['incurredAt'] = Carbon::parse($projectCostItem['incurredAt']);
            } catch (Exception $e) {
                throw new AppException('Cannot parse incurred at date - Reason: ' . $e->getMessage());
            }

            //type
            if (!isset($projectCostItem['type'])) {
                throw new AppException('No project type defined');
            }
            $projectCostType = $projectCostItem['type'];

            if (!is_numeric($projectCostType)) {
                throw new AppException('Project type is not numeric');
            }
            if (!array_key_exists($projectCostType, ProjectCost::getTypes())) {
                throw new AppException('Project type is not valid');
            }

            //amount
            if (!isset($projectCostItem['amount'])) {
                throw new AppException('No project cost amount defined');
            }
            if (preg_match('/-?([0-9])/', $projectCostItem['amount']) !== 1) {
                throw new AppException($projectCostItem['amount'] . ' is not a valid format');
            }

            switch ($projectCostType) {
                case ProjectCostType::TYPE_GENERAL:
                    //User Role Permissions
                    checkRoles($user, $roles['editCostsGeneral'], 'User does not have proper role to edit project cost.');

                    //projectPaymentTypeID
                    if (isset($projectCostItem['projectPaymentTypeID']) && $projectCostItem['projectPaymentTypeID'] != '') {
                        if (!is_numeric($projectCostItem['projectPaymentTypeID'])) {
                            throw new AppException('Project payment type ID is not numeric');
                        }
                        if (($projectPaymentType = ProjectPaymentType::find($projectCostItem['projectPaymentTypeID'])) === null) {
                            throw new AppException('Unable to find payment type');
                        }
                    }

                    break;
                case ProjectCostType::TYPE_COMMISSION:
                    //User Role Permissions
                    checkRoles($user, $roles['editCostsCommission'], 'User does not have proper role to edit project cost.');

                    //userID
                    if (!isset($projectCostItem['userID'])) {
                        throw new AppException('No commission user ID defined');
                    }
                    if (!is_numeric($projectCostItem['userID'])) {
                        throw new AppException('Commission user ID is not numeric');
                    }
                    if (($commissionUser = User::find($projectCostItem['userID'])) === null) {
                        throw new AppException('Unable to find commission user');
                    }
                    if ($commissionUser->companyID !== $user->companyID) {
                        throw new AppException('Commission user ID is not assigned to the correct company');
                    }
                    break;
            }

            //Passed Validation
            if ($costAction === 'edit') {
                //Update Project Cost
                $projectCost->incurredAt = $projectCostItem['incurredAt'];
                $projectCost->updatedAt = $now;
                $projectCost->updatedByUserID = $user->userID;
                $projectCost->save();

                if ($projectCostType == ProjectCostType::TYPE_GENERAL) {
                    //Update General Costs
                    $projectCost->cost->description = $projectCostItem['description'] === '' ? null : $projectCostItem['description'];
                    $projectCost->cost->projectPaymentTypeID = $projectCostItem['projectPaymentTypeID'];
                    $projectCost->cost->amount = $projectCostItem['amount'];
                    $projectCost->cost->updatedAt = $now;
                    $projectCost->cost->updatedByUserID = $user->userID;
                    $projectCost->cost->save();

                } else if ($projectCostType == ProjectCostType::TYPE_COMMISSION){
                    //Update Commission Costs
                    $projectCost->cost->userID = $projectCostItem['userID'];
                    $projectCost->cost->amount = $projectCostItem['amount'];
                    $projectCost->cost->updatedAt = $now;
                    $projectCost->cost->updatedByUserID = $user->userID;
                    $projectCost->cost->save();
                }

            } else if ($costAction === 'new') {

                if ($projectCostType == ProjectCostType::TYPE_GENERAL) {
                    //Add General Cost
                    $generalCost = ProjectGeneralCost::create([
                            'description' => $projectCostItem['description'] === '' ? null : $projectCostItem['description'],
                            'projectPaymentTypeID' => $projectCostItem['projectPaymentTypeID'],
                            'amount' => $projectCostItem['amount'],
                            'createdAt' => $now,
                            'createdByUserID' => $user->userID,
                            'updatedAt' => $now,
                            'updatedByUserID' => $user->userID
                        ]);
                    $costID = $generalCost->projectGeneralCostID;

                } else if ($projectCostType == ProjectCostType::TYPE_COMMISSION){
                    //Add Commission Cost
                    $commissionCost = ProjectCommissionCost::create([
                            'userID' => $projectCostItem['userID'],
                            'amount' => $projectCostItem['amount'],
                            'createdAt' => $now,
                            'createdByUserID' => $user->userID,
                            'updatedAt' => $now,
                            'updatedByUserID' => $user->userID
                        ]);
                    $costID = $commissionCost->projectCommissionCostID;
                }

                //Add Project Cost
                ProjectCost::create([
                    'projectID' => $projectID,
                    'projectCostTypeID' => $projectCostItem['projectCostTypeID'],
                    'type' => $projectCostType,
                    'costID' => $costID,
                    'incurredAt' => $projectCostItem['incurredAt'],
                    'createdAt' => $now,
                    'createdByUserID' => $user->userID,
                    'updatedAt' => $now,
                    'updatedByUserID' => $user->userID
                ]);
            }

            break;
        case 'deleteProjectCost':
            //User Role Permissions
            checkRoles($user, $roles['deleteCosts'], 'User does not have proper role to delete project costs.');

            //projectCostID
            if (($projectCostID = Input::post('projectCostID')) === null) {
                throw new AppException('No project cost ID defined');
            }
            if (!is_numeric($projectCostID)) {
                throw new AppException('Project cost ID is not numeric');
            }
            if (($projectCost = ProjectCost::with('cost')->find($projectCostID)) === null) {
                throw new AppException('Unable to find project cost item');
            }

            //ProjectCost Item
            if ($projectCost->project->property->customer->companyID !== $user->companyID) {
                throw new AppException('Project cost item not assigned to proper company');
            }

            //projectID
            if ($projectCost->projectID != $projectID) {
                throw new AppException('Project ID does not match this project cost item project ID');
            }

            //Delete Project Cost
            //@TODO Change this back to soft delete system once timezone is corrected
            $projectCost->deletedAt = $now;
            $projectCost->deletedByUserID = $user->userID;
            $projectCost->save();

            //Delete Project Cost General or Comission
            $projectCost->cost->deletedAt = $now;
            $projectCost->cost->deletedByUserID = $user->userID;
            $projectCost->cost->save();

            break;
    }


} catch (AppException $e) {
    $json['status'] = 0;
    $json['error'] = [
        'code' => $e->getCode(),
        'message' => $e->getMessage()
    ];
}

header('Content-Type: application/json');
echo json_encode($json);
