<?php

use App\Services\TimeService;
use Core\StaticAccessors\App;

/** @var TimeService $time_service */
$time_service = App::get(TimeService::class);

include_once(__DIR__ . '/../includes/classes/class_Company.php');
include_once(__DIR__ . '/../includes/classes/class_User.php');

if(isset($_SESSION["userID"])) {
    $userID = $_SESSION['userID'];
}
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
        $metrics = $userArray['metrics'];

        $companyObject = new Company();
        $companyObject->setCompany($companyID);
        $companyObject->getCompany();
        if (($company = $companyObject->getResults()) === null) {
            throw new Exception('Unable to find company');
        }
        $timezone = $company['timezone'];
        $daylightSavings = $company['daylightSavings'];

        if ($primary == 1 || $metrics == 1) {

            if(isset($_POST['dateFrom'])) {
                $date_from = trim($_POST['dateFrom']);
                try {
                    $dateFrom = $time_service->getUtc($date_from)->format('Y-m-d H:i:s');
                } catch (Exception $e) {
                    throw new Exception('From date is not valid');
                }
            }

            if(isset($_POST['dateTo'])) {
                $date_to = trim($_POST['dateTo']);
                try {
                    $dateTo = $time_service->getUtc($date_to)->format('Y-m-d H:i:s');
                } catch (Exception $e) {
                    throw new Exception('To date is not valid');
                }
            }
			
			include_once(__DIR__ . '/../includes/classes/class_Metrics_AvgPricePerProjectsBid.php');
					
				$object = new Metrics();
				$object->setMetrics($companyID, $dateFrom, $dateTo);
				$object->getMetrics();
				
				$metricArray = $object->getResults();	

				if ($metricArray != '') {

					echo json_encode($metricArray);
				}

		}
		
?>