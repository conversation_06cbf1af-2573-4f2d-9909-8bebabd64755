<?php

use App\Classes\Acl;
use App\Classes\Func;
use App\Classes\Template;
use App\Resources\CompanyResource;
use App\Services\CompanyFeatureService;
use App\Services\CompanySettingService;
use App\Services\Payment\PaymentLinkService;
use Common\Models\EmailTemplate;
use Common\Models\Feature;
use Core\Classes\File;
use Core\Components\Asset\StaticAccessors\Asset;
use Core\Components\Http\StaticAccessors\URI;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Dompdf\Dompdf;

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID']; 
	} 

	$creditMemoDisplay = NULL;
    $sendEmail = NULL;

	// Payment link functionality
	$payment_button_html = "";
	$show_payment_button = false;
	$company_color_button_color = "#ffffff"; // Default button text color for company branded buttons

	include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		
	$customEvaluation = '';
	$companyPhoneDisplay = '';
	$companyPhoneDisplayEmail = '';

    $logoUrl = null;
    $logoHtml = null;

	if(isset($_GET['eid'])) {
		$evaluationID = filter_input(INPUT_GET, 'eid', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if(isset($_GET['custom'])) {
		$customEvaluation = filter_input(INPUT_GET, 'custom', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

    if(isset($_GET['email'])) {
        $sendEmail = filter_input(INPUT_GET, 'email', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    }

	if ($customEvaluation == 0){
		$customEvaluation = '';
	}

include_once(__DIR__ . '/includes/classes/class_EvaluationProject.php');
			
		$object = new EvaluationProject();
		$object->setEvaluation($evaluationID, $companyID, $customEvaluation);
		$object->getEvaluation();
		$projectArray = $object->getResults();	

		//Project
		$projectID = $projectArray['projectID'];
		$projectDescription = $projectArray['projectDescription'];
		$propertyID = $projectArray['propertyID'];
		$customerID = $projectArray['customerID'];
		$firstName = $projectArray['firstName'];
		$lastName = $projectArray['lastName'];
		$businessName = $projectArray['businessName'];
		$address = $projectArray['address'];
		$address2 = $projectArray['address2'];
		$city = $projectArray['city'];
		$state = $projectArray['state'];
		$zip = $projectArray['zip'];
		$ownerAddress = $projectArray['ownerAddress'];
		$ownerAddress2 = $projectArray['ownerAddress2'];
		$ownerCity = $projectArray['ownerCity'];
		$ownerState = $projectArray['ownerState'];
		$ownerZip = $projectArray['ownerZip'];
		$email = $projectArray['email'];
		$bidFirstSent = $projectArray['bidFirstSent'];
		$bidAccepted = $projectArray['bidAccepted'];
		$bidAcceptanceName = $projectArray['bidAcceptanceName'];
		$bidAcceptanceAmount = $projectArray['bidAcceptanceAmount'];
		$bidAcceptanceNumber = $projectArray['bidAcceptanceNumber'];
		$projectCompleteName = $projectArray['projectCompleteName'];
		$projectCompleteAmount = $projectArray['projectCompleteAmount'];
		$projectCompleteNumber = $projectArray['projectCompleteNumber'];

		$bidScopeChangeTotal = $projectArray['bidScopeChangeTotal'];
		$bidScopeChangeTotal = number_format($bidScopeChangeTotal, 2, '.', ',');
		$bidScopeChangeType = $projectArray['bidScopeChangeType'];
		$bidScopeChangeNumber = $projectArray['bidScopeChangeNumber'];
		$bidScopeChangeQuickbooks = $projectArray['bidScopeChangeQuickbooks'];

		$createdFirstName = $projectArray['createdFirstName'];
		$createdLastName = $projectArray['createdLastName'];
		$createdEmail = $projectArray['createdEmail'];
		$createdPhone = $projectArray['createdPhone'];			

		$invoicePaid = false;
		if ($bidScopeChangeType == '0') {
			$bidScopeChangeTypeDisplay = 'Invoice';
            if ($projectArray['bidScopeChangePaid'] == 1) {
                $invoicePaid = true;
            }
		} else if ($bidScopeChangeType == '1') {
            $bidScopeChangeTypeDisplay = 'Credit';
		}

		include_once(__DIR__ . '/includes/classes/class_Company.php');
			
			$object = new Company();
			$object->setCompany($companyID);
			$object->getCompany();
			$companyArray = $object->getResults();		
			
			//Company
			$companyID = $companyArray['companyID'];
			$companyName = $companyArray['name'];

			//check for billing address
			//if it exists, then use the billing address on the invoice.
			//if not, then proceed as normal
			$companyBillingAddress1 = trim($companyArray['billingAddress']);

			if ($companyBillingAddress1 !=''){
				$companyAddress1 = $companyArray['billingAddress'];
				$companyAddress2 = $companyArray['billingAddress2'];
				$companyCity = $companyArray['billingCity'];
				$companyState = $companyArray['billingState'];
				$companyZip = $companyArray['billingZip'];
			}else{
				$companyAddress1 = $companyArray['address'];
				$companyAddress2 = $companyArray['address2'];
				$companyCity = $companyArray['city'];
				$companyState = $companyArray['state'];
				$companyZip = $companyArray['zip'];
			}
			
			$companyWebsite = $companyArray['website'];
			$companyEmailReply = $companyArray['emailReply'];
			$companyEmailFrom = $companyArray['emailFrom'];
			$timezone = $companyArray['timezone'];
			$daylightSavings = $companyArray['daylightSavings'];

            $website_display = '';
            if ($companyWebsite !== null) {
                $website_display = '<a href="http://'.$companyWebsite.'">'.$companyWebsite.'</a>';
            }

            $setting_service = new CompanySettingService((int) $companyID);
            $email_greeting = $setting_service->get('email_greeting', null);
            $email_greeting =  $email_greeting ?: 'Hello';

            $email_template_data = EmailTemplate::where('ownerID', $companyID)
                ->where('type', EmailTemplate::TYPE_INVOICE)
                ->first();
            $companyEmailInvoicePlain = strip_tags($email_template_data->content);
            $companyEmailInvoice = htmlspecialchars_decode($email_template_data->content);

            $email_paid_template_data = EmailTemplate::where('ownerID', $companyID)
                ->where('type', EmailTemplate::TYPE_INVOICE_PAID)
                ->first();
            $companyEmailInvoicePaidPlain = strip_tags($email_paid_template_data->content);
            $companyEmailInvoicePaid = htmlspecialchars_decode($email_paid_template_data->content);


            $companyColor = $companyArray['color'];
            $companyColorHover = $companyArray['colorHover'];

            $logoFileID = $companyArray['logoFileID'];
            if (!empty($logoFileID)) {
                $companyResource = CompanyResource::make(Acl::make(Auth::user()));
                $logoUrl = $companyResource->getMedia()->get('logo')->getVariant('email_thumbnail')->getUrl($companyID)->csm()->build();
                $logoHtml = '<p style="text-align:center"><img alt="Company Logo" src="'.$logoUrl.'" /></p>';
            }

if ($companyAddress2 == '') {
			$companyAddressBlock = '
				'.$companyAddress1.'<br/>
				'.$companyCity.', '.$companyState.' '.$companyZip.'<br/>';
		} else {
			$companyAddressBlock = '
				'.$companyAddress1.', '.$companyAddress2.'<br/>
				'.$companyCity.', '.$companyState.' '.$companyZip.'<br/>';
		}


		//Phone	
		include_once(__DIR__ . '/includes/classes/class_CompanyPhone.php');
				
			$object = new CompanyPhone();
			$object->setCompany($companyID);
			$object->getPhone();
			$phoneArray = $object->getResults();	
			
			foreach($phoneArray as &$row) {
				$phoneNumber = $row['phoneNumber'];
				$phoneDescription = $row['phoneDescription'];
				$isPrimary = $row['isPrimary'];

				$companyPhoneDisplayEmail .= '
					'.$phoneDescription.' '.$phoneNumber.'<br/>';
				 		
			}
	

		include_once(__DIR__ . '/includes/classes/class_ScopeChanges.php');
			
		$object = new ScopeChanges();
		$object->setEvaluation($evaluationID, $companyID);
		$object->getEvaluation();
		$evaluationArray = $object->getResults();	
		
		if ($evaluationArray != '') {
		
			foreach ( $evaluationArray as &$row){
				$date = $row['date'];
				$item = $row['item'];
				$price = $row['price'];
				$price = number_format($price, 2, '.', ',');
				$type = $row['type'];
				
				if ($date != NULL) { 
					$date = date('m/d/Y', strtotime($date));
				}

				$creditMemoDisplay .= 
					'<tr>
		                <td style="border: 1px solid #000000; cellspacing="0">'.$item.'</td>
		                <td style="border: 1px solid #000000; text-align: right;">$'.$price.'</td>
		            </tr>';
				
			}
			
		} 



try {
	if ($bidScopeChangeType == '0' && !$invoicePaid) {
		// Check if payments feature is enabled
		$company_feature = new CompanyFeatureService($companyID);
		$is_payments_enabled = $company_feature->has(Feature::PAYMENTS, false);
		
		if ($is_payments_enabled) {
			$payment_link_service = new PaymentLinkService();
			$payment_link = $payment_link_service->findByCompanyID($companyID);
			
			if ($payment_link) {
				$invoice_type_map = [
					'scope_change' => 4,
				];
				$invoice_type_param = $invoice_type_map['scope_change'];

				$payment_url = URI::route('payments.link.charge', ['shortToken' => $payment_link->shortToken])
					->query([
						'invoice_type' => $invoice_type_param,
						'bid_id' => $evaluationID
					])
					->build();

				$show_payment_button = true;
				$payment_button_html = '<a href="' . htmlspecialchars($payment_url, ENT_QUOTES, 'UTF-8') . '" target="_blank"
				 style="display:inline-block; text-align:center; width:100px;
						background-color:' . htmlspecialchars($companyColor, ENT_QUOTES, 'UTF-8') . ';
						color:#ffffff; padding:10px; border-radius:5px; text-decoration:none;">
				 Pay Now
			   </a>';
			}
		}
	}
	
	if (!$show_payment_button) {
		$payment_button_html = '<div style="width: 100px;">&nbsp;</div>'; // Empty placeholder to maintain layout
	}
	
} catch (Exception $e) {
	error_log("Payment button generation error for evaluation {$evaluationID}: " . $e->getMessage());
	$payment_button_html = '<div style="width: 100px;">&nbsp;</div>'; // Empty placeholder on error
}

$dompdf = new DOMPDF([
    'isRemoteEnabled' => true,
    'chroot' => realpath(Path::get('company', $companyID))
]);
$date =  date('F j, Y');

    $html =
      '<html>
         <style>
            body { padding:10px 30px 10px 30px; font-family: sans-serif; }
            .header { position: fixed; left: 0px; top: -180px; right: 0px; height: 150px; background-color: orange; text-align: center; }
            .footer { position: fixed; left: 0px; bottom: -150px; right: 0px; height: 150px; text-decoration: underline; text-align:center;font-family:times;font-weight:normal; }
            p {margin-top:0;}
            h1, h2, h3, h4 {margin-top:0;margin-bottom:0; }
        </style>
        <body>
            <table style="height: 128px;width:650px;">
                <tbody>
                    <tr>
                        <td style="width:370px;">
                            <h1>'.$companyName.'</h1><br/>
                            '.$companyAddressBlock.'
                            '.$companyPhoneDisplayEmail.'
                        </td>
                        <td>
                            <table style="height: 40px; float: right;width:270px;" cellspacing="0">
                                <tbody>
                                    <tr>
                                        <td style="width:65px;"></td>
                                        <td style="width:100px;">
                                            <h1 style="text-align: right;"><span style="color: #808080;">'.strtoupper($bidScopeChangeTypeDisplay).'</span></h1>
                                            <br></br>
                                            <br></br>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="text-align: center; background-color: #cccccc;border: 1px solid #000000;width:65px;">'.strtoupper($bidScopeChangeTypeDisplay).' #</td>
                                        <td style="text-align: center; background-color: #cccccc;border: 1px solid #000000; width:100px;">DATE</td>
                                    </tr>
                                    <tr> 
                                        <td style="text-align: center;border: 1px solid #000000;">'.$bidScopeChangeNumber.'</td>
                                        <td style="text-align: center;border: 1px solid #000000;">'.$date.'</td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table style="height: 40px; border-collapse: collapse;width:650px;margin-top:30px;">
                <tbody>
                    <tr>
                        <td style="width:50%;">' . $payment_button_html . '</td>
                        <td style="width:50%;">
                            &nbsp;
                        </td>
                    </tr>
                </tbody>
            </table>
            <table style="width: 650px; margin-top: 40px; border-collapse: collapse;">
                <tr>
                    <td style="width: 325px;">
                        <table style="height: 150px; width:100%;">
                            <tbody>
                                <tr>
                                    <td style="font-size: 14px; text-align: center; border: 1px solid #000000; background-color: #cccccc;">BILL TO</td>
                                </tr>
                                <tr>
                                    <td>
                                        '.($businessName != '' ? $businessName . '<br>' : '').'
                                        '.$firstName.' '.$lastName.'
                                        <br>'.$ownerAddress.' '.$ownerAddress2.'</br>
                                        <br>'.$ownerCity.', '.$ownerState.' '.$ownerZip.'</br>
                                        <br>'.$email.'</br>
                                    </td>
                                </tr>
                            </tbody>
                        </table>    
                    </td>
                    <td style="text-align: right; vertical-align: top;">
                        ' . ($invoicePaid ? '<img src="' . Asset::uri('image', 'paid.png')->build() . '" style="width:150px;" />' : '&nbsp;') . '
                    </td>
                </tr>
            </table>
            <table style="height: 40px; border: 1px solid #000000; border-collapse: collapse;width:650px;">
                <tbody>
                    <tr style="height: 20px; background-color: #cccccc;">
                        <td style="border: 1px solid #000000;">DESCRIPTION</td>
                        <td style="border: 1px solid #000000; text-align: center;">AMOUNT</td>
                    </tr>
                    '.$creditMemoDisplay.'
                    <tr style="height: 90px;">
                        <td style="border: 1px solid #000000; text-align: center;"><em>Thank you for your business!</em>
                        </td>
                        <td style="border: 1px solid #000000; font-size: 20px; text-align: right;"><strong>TOTAL:  $'.$bidScopeChangeTotal.'</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </body>
    
    </html>';

    if ($sendEmail == 'send') {

        $propertyAddress = $address;
        $propertyAddress2 = Func::emptyToNull($address2);
        $propertyAddress .= $propertyAddress2 !== null ? ', ' . $propertyAddress2 : '';

        switch($bidScopeChangeType) {
            case '0': // invoice
                if ($invoicePaid) {
                    $companyEmailScopeChangePlain = strip_tags($companyEmailInvoicePaid);
                    $companyEmailScopeChange = htmlspecialchars_decode($companyEmailInvoicePaid);
                } else {
                    $companyEmailScopeChangePlain = strip_tags($companyEmailInvoice);
                    $companyEmailScopeChange = htmlspecialchars_decode($companyEmailInvoice);
                }

                break;
            case '1': // scope change
                $creditMemoContent = '<p>Please find your credit memo attached. &nbsp;Let us know if you have any questions.&nbsp;</p>';
                $companyEmailScopeChangePlain = strip_tags($creditMemoContent);
                $companyEmailScopeChange = htmlspecialchars_decode($creditMemoContent);
        }

        $dompdf->load_html($html);
        $dompdf->render();

        $file_invoice = Func::createTempFile('pdf');
        if ($file_invoice->fwrite($dompdf->output()) === 0) {
            throw new AppException('No data written to invoice temp file: %s', $file_invoice->getPathname());
        }
        unset($dompdf);

        $body = "
                        <html>
                            <style type=\"text/css\">
                                body {
                                    height:100% !important;
                                    background-color:#ffffff;
                                    margin:0;
                                    color:#151719;
                                    font-family: \"Helvetica Neue\", Helvetica, Roboto, Arial, sans-serif;
                                    line-height:1.5;
                                }
                                div.emailContent {
                                    width:100%;
                                    margin:0px auto 0 auto;
                                    padding:10px 0 15px 0;
                                    background-color:#ffffff;
                                }
                                div.inner {
                                    margin:0px 30px 0px 30px;
                                }
                                div.emailFooter {
                                    width:100%;
                                }
    
                                span.highlight {
                                    color:".$companyColor.";
                                    font-weight:bold;
                                }
    
                                a {
                                    color:".$companyColor.";
                                }
    
                                a:visted {
                                    color:".$companyColor.";
                                }
                            </style>
                            <body>
                                <div class=\"emailContent\">
                                    <div class=\"inner\">
                                        <p>
                                            ".$email_greeting." ".$firstName.",
                                        </p>
                                        ".$companyEmailScopeChange."
                                       ".$logoHtml."
                                    </div>
                                </div>
                                <div class=\"emailFooter\">
                                    <div class=\"inner\">
                                        <p style=\"text-align:center;padding-bottom:20px;\">
                                            <span class=\"highlight\">".$companyName."</span> | ".$companyAddress1.", ".$companyCity.", ".$companyState." ".$companyZip."<br/>
                                            ".$companyPhoneDisplayEmail."
                                            ".$website_display."
                                        </p>
                                    </div>
                                </div>
                            </body>
                        </html>
                        ";

        $subject = '';
        if ($bidScopeChangeType == '0') {
            if ($invoicePaid) {
                $subject = Template::replace($email_paid_template_data->subject, [
                    'company_name' => $companyName,
                    'address' => $propertyAddress
                ]);
            } else {
                $subject = Template::replace($email_template_data->subject, [
                    'company_name' => $companyName,
                    'address' => $propertyAddress
                ]);
            }
        } else if ($bidScopeChangeType == '1') {
            $subject = 'Credit' . ' - ' . $propertyAddress;
        }

        if ($invoicePaid) {
            $altbody = $companyEmailInvoicePaidPlain;
        } else {
            $altbody = $companyEmailInvoicePlain;
        }

        $mail = FXEmail::type(FXEmail::TYPE_CUSTOMER)
            ->customerID($customerID)
            ->projectID($projectID);
        if ($mail->canSend()) {
            $mail->subject($subject);
            $mail->attach($file_invoice->getPathname(), File::sanitizeName("{$firstName}_{$lastName}_Scope_Change_{$bidScopeChangeTypeDisplay}", 'pdf'));
            $mail->html($body, $altbody);
            $mail->send();
        }

        if (!@unlink($file_invoice->getPathname())) {
            throw new AppException('Unable to delete temp invoice file: %s', $file_invoice->getPathname());
        }

//        include(__DIR__ . '/includes/classes/class_EditInvoiceLastSent.php');
//        $object = new Invoice();
//        $object->setEvaluation($evaluationID, $customEvaluation, $invoiceName, $invoiceType, $userID);
//        $object->setInvoice();
//        $response = $object->getResults();

        //if ($response == 'true') {
            echo "<script>window.close();</script>";
        //}

    } else {
        $dompdf->load_html($html);
        $dompdf->render();
        //$dompdf->stream( $firstName.'-'.$lastName.'-Invoice');//Direct Download
        $dompdf->stream($firstName.'-'.$lastName.'-Scope-Change-'.$bidScopeChangeTypeDisplay,array('Attachment'=>0));//Display in Browser
    }
?>