<?php

use App\Classes\GoogleMap;
use App\Exceptions\ApiException;
use App\Resources\LeadResource;
use App\Resources\TaskResource;
use App\Services\CompanyFeatureService;
use App\Services\TimeService;
use Common\Models\Feature;
use Core\Components\Http\StaticAccessors\URI;
use Core\StaticAccessors\{App, Config};
use Core\Components\Resource\Classes\Scope;

/** @var TimeService $time_service */
    $time_service = App::get(TimeService::class);
	
	$companyProfileDisplay = NULL;
	$accountDisplay = NULL;
	$metricsNavDisplay = NULL;
	$setupDisplay = NULL;
	$crewManagementNavDisplay = NULL;
	$marketingNavDisplay = NULL;
	$brandingStylesheet = NULL;
	$backToProject = NULL;
	$brandingFavicon = NULL;
    $addPropertyButton = null;
    $viewPropertiesButton = null;
    $editInfoButton = null;
    $viewStatementButton = null;
	
	if(isset($_GET['cid'])) {
		$customerID = filter_input(INPUT_GET, 'cid', FILTER_SANITIZE_NUMBER_INT);
	}
	$projectIDNew = NULL;
	if(isset($_GET['pid'])) {
		$backToProjectID = filter_input(INPUT_GET, 'pid', FILTER_SANITIZE_NUMBER_INT);
	}

    $query_property_id = null;
    if(isset($_GET['tid'])) {
        $query_property_id = filter_input(INPUT_GET, 'tid', FILTER_SANITIZE_NUMBER_INT);
    }
	
	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID'];
	}

    $lists = Config::get('lists');

	include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$userFirstName = $userArray['userFirstName'];
		$userLastName = $userArray['userLastName'];
		$userPhoneDirect = $userArray['userPhoneDirect'];
		$userPhoneCell = $userArray['userPhoneCell'];
		$userEmail = $userArray['userEmail'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$marketing = $userArray['marketing'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$calendarBgColor = $userArray['calendarBgColor'];
		$timezone = $userArray['timezone'];
		$daylightSavings = $userArray['daylightSavings'];
		$timecardApprover = $userArray['timecardApprover'];

		if(!empty($backToProjectID)){
            $backToProjectID = URI::route('page.app.projects', [
                'path' => '/'.$backToProjectID
            ])->build();
			$backToProject = '<div class="medium-3 columns back-to-project"><a style="font-weight: bold;font-size: 1rem;float: right;margin-top: 1.5rem;" href="'.$backToProjectID.'">Back To Project <span></span></a></div>';
		}

    $company_feature = new CompanyFeatureService($companyID);
    $task_feature = $company_feature->has(Feature::TASKS, true);
    $task_association_customer = TaskResource::ASSOCIATION_TYPE_CUSTOMER;
    $task_status_active = TaskResource::STATUS_ACTIVE;
    $task_status_completed = TaskResource::STATUS_COMPLETED;

    $page_customer_tasks = URI::route('page.app.tasks', [
        'path' => ''
    ])->build() . '?association_type='.$task_association_customer.'&association_id='.$customerID.'&status=in:'.$task_status_active.'|'.$task_status_completed;

    $page_create_task = URI::route('page.app.tasks', [
        'path' => '/create?customer_id='.$customerID
    ])->build();

    $page_account = URI::route('page.app.company.account', [
        'path' => ''
    ])->build();

    $task_resource = TaskResource::make(Auth::acl());
    $task_scope = Scope::make()
        ->fields(['id', 'title'])
        ->filter('association_type', 'eq', TaskResource::ASSOCIATION_TYPE_CUSTOMER)
        ->filter('association_id', 'eq', $customerID);
    $tasks = $task_resource->collection()->scope($task_scope)->run()->toArray();

	$phoneDisplay= NULL;
    $taskDisplay = null;
	$customerNameSectionDisplay = NULL;
	$propertyDisplay = NULL;
	$projectDisplay = NULL;
	$last_property_id = NULL;
	$last_project_id = NULL;
	$leadDisplay = NULL;

	if ($task_feature) {
	    $task_count = '<br><i>No Tasks</i>';
	    $task_array_count = count($tasks);
        $task_list = '';
        foreach ($tasks as $task) {
            $task_list .= $task['title'] . '<br>';
        }
        if ($task_list !== '') {
            $task_list = '<br>' . $task_list;
        }
        if ($task_array_count === 1) {
            $task_count = '<br><a target="_blank" href="'.$page_customer_tasks.'">View '.$task_array_count.' Task</a>';
        } else if ($task_array_count > 1) {
            $task_count = '<br><a target="_blank" href="'.$page_customer_tasks.'">View '.$task_array_count.' Tasks</a>';
        }
	    $taskDisplay = '<a href="'.$page_create_task.'" target="_blank">Add Task</a>' . $task_count . $task_list;
    } else {
        $taskDisplay = '<br><a id="task-feature-not-enabled">View Tasks</a>';
    }

    $task_feature_not_enabled_content = '';
    if ($primary) {
        $task_feature_not_enabled_content = 'Your company is on a Legacy plan not eligible for this feature. Please <a href="'.$page_account.'">upgrade</a> or <a href="mailto:<EMAIL>">contact us</a> to learn more.';
    } else {
        $task_feature_not_enabled_content = 'Your company is on a Legacy plan not eligible for this feature. Please contact your internal administrator to upgrade your account.';
    }
		
	//Customer	
	include_once(__DIR__ . '/includes/classes/class_Customer.php');
			
		$object = new Customer();
		$object->setCustomer($customerID, $companyID);
		$object->getCustomer();
		$customerArray = $object->getResults();

		if (empty($customerArray)) {
			return Response::redirect()->to('/');
			
		} else {
		
			$leadID = $customerArray['leadID'];
			$firstName = $customerArray['firstName'];
			$lastName = $customerArray['lastName'];
			$ownerAddress = $customerArray['ownerAddress'];
			$ownerAddress2 = $customerArray['ownerAddress2'];
			$ownerCity = $customerArray['ownerCity'];
			$ownerState = $customerArray['ownerState'];
			$ownerZip = $customerArray['ownerZip'];
			$email = $customerArray['email'];
			$businessName = $customerArray['businessName'];
			$unsubscribed = $customerArray['unsubscribed'];
			$noEmailRequired = $customerArray['noEmailRequired'];
			
			if ($unsubscribed == '1') {
				$unsubscribedText = ' <span>unsubscribed</span>';
			} else {
				$unsubscribedText = '';
			}
			if ($noEmailRequired == '1') {
				$unsubscribedText = ' <span>no email</span>';
			}

            $customerNameDisplay = $firstName . ' ' . $lastName;
            if (!empty($businessName)){
                $customerNameDisplay = $businessName;
                $businessNameDisplay = 'Business Name: ' . $businessName . '<br>';
                $customerNameSectionDisplay = 'Customer Name: ' . $firstName . ' ' . $lastName . '<br>';

            }

            if (!empty($leadID)) {
                $lead_resource = LeadResource::make(Auth::acl());
                $lead_scope = Scope::make()
                    ->fields(['lead_uuid']);
                try {
                    $lead = $lead_resource->entity($leadID)->scope($lead_scope)->run();

                    $page_customer_lead = URI::route('page.app.leads', [
                            'path' => ''
                        ])->build() . '/details/'.$lead->lead_uuid;

                    $leadDisplay = 'View Lead';
                    $leadDisplay = '<a href="'.$page_customer_lead.'">Go To Lead</a>';
                } catch (Throwable $e) {}
            }
			
			//Phone	
			include_once(__DIR__ . '/includes/classes/class_CustomerPhone.php');
					
				$object = new CustomerPhone();
				$object->setCustomer($customerID);
				$object->getPhone();
				$phoneArray = $object->getResults();	
				
				if (!empty($phoneArray)) {
					foreach($phoneArray as &$row) {
						$phoneNumber = $row['phoneNumber'];
						$phoneDescription = $row['phoneDescription'];
						$isPrimary = $row['isPrimary'];
						
						if ($isPrimary == '1') {
							$primaryPhone = ' <span>primary</span>';
						} else {
                            $primaryPhone = '';
						}
						
						$phoneDisplay .= '
							'.$phoneDescription.': <a href="tel:'.$phoneNumber.'">'.$phoneNumber.'</a>'.$primaryPhone.'<br/>';
					}
				}
			
			//Properties
			include_once(__DIR__ . '/includes/classes/class_CustomerProjects.php');
					
				$object = new Projects();
				$object->setCustomer($customerID, $companyID);
				$object->getProjects();
				$projectArray = $object->getResults();	
				
				if (!empty($projectArray)) {
					foreach($projectArray as &$row) {
						$propertyID = $row['propertyID'];
						$address = $row['address'];
						$address2 = $row['address2'];
						$city = $row['city'];
						$state = $row['state'];
						$zip = $row['zip'];
						$projectID = $row['projectID'];
						$projectDescription = $row['projectDescription'];
						$projectAdded = $row['projectAdded'];

                        $projectAdded = $time_service->getFromUtc($projectAdded)->format('F j, Y g:i a');
						
						//$last_property_id = null;
						
						if ($propertyID != $last_property_id ) {

							if ($address2 == NULL || $address2 == ''){
								$addressTitle = $address;
							} else {
                                $addressTitle = $address.' '.$address2;
							}

                            $directionsLink = GoogleMap::directionsUrl($address.' '.$city.' '.$state.' '.$zip);
                            $directions = '<a target="_blank" href="'.$directionsLink.'">Directions</a>';

							if ($propertyDisplay != NULL) {
                                $propertyDisplay .= '</p></div>';
							    if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
                                    $propertyDisplay .= '
                                        <div class="row expanded">
                                            <div class="columns"><a style="margin-left:.5rem;" href="customer-add.php?pid='.$last_property_id.'" class="button xtiny right">Add Project</a><button class="button xtiny secondary right" id="moveProperty">Move Property</button></div>
                                        </div>';
                                }
                                $propertyDisplay .= '</p></div>';
                            }
							$last_property_id = $propertyID;
                            $is_targeted_property = $propertyID == $query_property_id ? ' t-target' : '';
							$propertyDisplay .= '<div class="callout primary'.$is_targeted_property.'" id="'.$last_property_id.'">
								<div class="medium-12 columns no-pad" style="margin-bottom:.5rem;"> 
									<h5 style="margin:0;display: inline">' .$addressTitle. ', '.$city.', '.$state.' '.$zip.'</h5> <span>('.$directions.')</span>
								</div>
								<div class="medium-12 columns no-pad" style="margin-bottom:.5rem;">
								    <h6 class="no-margin">Projects</h6>
								    <p style="margin-left:1rem;">';
						}

                        $project_management_link = URI::route('page.app.projects', [
                            'path' => '/'.$projectID
                        ])->build();
						$propertyDisplay .= '
							<a href="'.$project_management_link.'">'.$projectDescription.'</a>&nbsp;&nbsp;&nbsp;<span style="font-size:.7rem;">Created '.$projectAdded.'</span><br/>';
										
					}

					if ($propertyDisplay != NULL) {
					    $propertyDisplay .= '</p></div>';
                        if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
                            $propertyDisplay .= '
                                <div class="row expanded">
                                    <div class="columns no-pad"><a style="margin-left:.5rem;" href="customer-add.php?pid='.$last_property_id.'" class="button xtiny right">Add Project</a><button class="button xtiny secondary right" id="moveProperty">Move Property</button></div>
                                </div>';
                        }
                        $propertyDisplay .= '</div>';
                    }
				}

				if ($primary == 1 || $projectManagement == 1 || $sales == 1) {
                    $page_properties_all = URI::route('page.app.properties', [
                            'path' => ''
                        ])->build() . '?customer_id=' . $customerID;

				    $editInfoButton = '<button id="editCustomerInfo" class="button">Edit Info</button>';
                    $viewStatementButton = '<a target="_blank" href="statement.php?cid='.$customerID.'" class="button">View Statement</a>';
                    $viewPropertiesButton = '<a href="'.$page_properties_all.'" class="button xtiny secondary" style="float:right;">View All Properties</a>';
				    $addPropertyButton = '<a href="customer-add.php?cid='.$customerID.'" class="button xtiny" style="float:right;">Add Property</a>';
                }
		}

?>