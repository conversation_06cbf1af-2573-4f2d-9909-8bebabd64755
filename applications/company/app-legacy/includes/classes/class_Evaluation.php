<?php

	
	class Evaluation {
		
		private $db;
		private $companyID;
		private $evaluationID;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setEvaluation($companyID, $evaluationID) {
			$this->companyID = $companyID;
			$this->evaluationID = $evaluationID;
		}
			
			
		public function getEvaluation() {
			
			if (!empty($this->companyID) && !empty($this->evaluationID)) {
				
				$st = $this->db->prepare("SELECT
                    `e`.`evaluationID`,
                    `e`.`projectID`,
                    `e`.`evaluationDescription`,
                    `e`.`customEvaluation`,
                    HEX(`e`.`bidItemID`) as `bidItemID`,
                    `e`.`isPiering`,
                    `e`.`isWallRepair`,
                    `e`.`isWaterManagement`,
                    `e`.`isSupportPosts`,
                    `e`.`supportPostNotes`,
                    `e`.`isCrackRepair`,
                    `e`.`isMudjacking`,
                    `e`.`isPolyurethaneFoam`,
                    `e`.`structureType`,
                    `e`.`structureTypeOther`,
                    `e`.`frontFacingDirection`,
                    `e`.`isStructureAttached`,
                    `e`.`StructureAttachedDescription`,
                    `e`.`structureMaterial`,
                    `e`.`generalFoundationMaterial`,
                    `e`.`isWalkOutBasement`,
                    `e`.`evaluationCreated`,
                    `e`.`evaluationCreatedByID`,
                    `e`.`evaluationLastUpdated`,
                    `e`.`evaluationLastUpdatedByID`,
                    `e`.`isSendToEngineer`,
                    `e`.`evaluationCancelled`,
                    `e`.`evaluationCancelledByID`,
                    `e`.`evaluationFinalized`,
                    `e`.`evaluationFinalizedByID`,
                    `e`.`finalReportSent`,
                    `e`.`finalReportSentByID`
				FROM evaluation AS e

				JOIN project AS p ON p.projectID = e.projectID
				JOIN property AS t ON t.propertyID = p.propertyID
				JOIN customer AS c ON c.customerID = t.customerID

				WHERE e.evaluationID = :evaluationID AND e.deletedAt IS NULL AND c.companyID = :companyID LIMIT 1
				");
				//write parameter query to avoid sql injections
				$st->bindParam('evaluationID', $this->evaluationID);
				$st->bindParam('companyID', $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnEvaluation = $row;
						
					}
					
					$this->results = $returnEvaluation;
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>