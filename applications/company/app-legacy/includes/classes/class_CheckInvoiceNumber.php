<?php

	
	class CheckInvoiceNumber {
		
		private $db;
		private $invoiceNumber;
		private $bidID;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setInvoiceNumber($invoiceNumber, $bidID) {
			$this->invoiceNumber = $invoiceNumber;
			$this->bidID = $bidID;
		}
			
		public function getCompany() {

			$st = $this->db->prepare("SELECT c.companyID FROM 

				evaluationBid AS b
				JOIN evaluation AS e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
				JOIN project AS p ON p.projectID = e.projectID
				JOIN customer AS c ON c.customerID = p.customerID

				WHERE b.bidID = :bidID");

			$st->bindParam(':bidID', $this->bidID);		
			$st->execute();
			
			if ($st->rowCount()>=1) {
				while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
					$returnCompany = $row['companyID'];

				}
				$companyID = $returnCompany;

			} else {
				$st = $this->db->prepare("SELECT c.companyID FROM 

				customBid AS b
				JOIN evaluation AS e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
				JOIN project AS p ON p.projectID = e.projectID
				JOIN customer AS c ON c.customerID = p.customerID

				WHERE b.bidID = :bidID");

				$st->bindParam(':bidID', $this->bidID);		
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCompany = $row['companyID'];

					}
					$companyID = $returnCompany;
				} 
			}
			
			if ($companyID != '') {
			
				if (!empty($this->invoiceNumber)) {
					
					$st = $this->db->prepare("SELECT * FROM (

					(SELECT  bidAcceptanceNumber AS 'invoiceNumber' FROM `evaluationBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidAcceptanceNumber IS NOT NULL AND companyID = :companyID)

					UNION ALL

					(SELECT  projectCompleteNumber AS 'invoiceNumber' FROM `evaluationBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE projectCompleteNumber IS NOT NULL AND companyID = :companyID)

					UNION ALL

					(SELECT  bidAcceptanceNumber AS 'invoiceNumber' FROM `customBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidAcceptanceNumber IS NOT NULL AND companyID = :companyID)

					UNION ALL

					(SELECT  projectCompleteNumber AS 'invoiceNumber' FROM `customBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE projectCompleteNumber IS NOT NULL AND companyID = :companyID)

					UNION ALL

					(SELECT invoiceNumber FROM `evaluationInvoice` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE invoiceNumber IS NOT NULL AND companyID = :companyID)

				) as t WHERE invoiceNumber = :invoiceNumber ORDER BY `invoiceNumber` DESC LIMIT 1");
					
					//write parameter query to avoid sql injections
					$st->bindParam(':companyID', $companyID);
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					
					$st->execute();
					
					if ($st->rowCount()>=1) {
						while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
							$returnBids = $row;

							$this->results = $returnBids;
						}
						
						
					} 
					
				} 
			}
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>