<?php

use App\Resources\Bid\ItemResource;
use Core\Classes\File;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Resource\Classes\Entity;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Ramsey\Uuid\Uuid;

class Evaluation {
		
		private $db;
		private $companyID;
		private $projectID;
		private $userID;
		private $description;
		private $fileName;
		private $fileContent;
		private $guidedBid = false;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setEvaluation($companyID, $projectID, $userID, $description, $fileName, $fileContent) {
			$this->companyID = $companyID;
			$this->projectID = $projectID;
			$this->userID = $userID;
			$this->description = $description;
			$this->fileName = $fileName;
			$this->fileContent = $fileContent;
			
		}

		public function setGuidedBid($bool)
        {
            $this->guidedBid = $bool;
        }
			
		public function sendEvaluation() {
			
			if (!empty($this->projectID) && !empty($this->userID) && !empty($this->description)) {

                $bid_item_id = null;
			    if ($this->guidedBid) {
			        try {
                        $bid_item_id = ItemResource::make(Auth::acl())->create(Entity::make([
                            'type' => ItemResource::TYPE_GUIDED,
                            'project_id' => (int) $this->projectID,
                            'follow_up_notification_status' => ItemResource::FOLLOW_UP_NOTIFICATION_STATUS_DISABLED,
                            'is_locked' => false
                        ]))->run();
                    } catch (Exception $e) {
			            error_log($e);
			            return;
                    }
                }
				
				$st = $this->db->prepare("INSERT INTO `evaluation`
					(
					`evaluationUUID`,
					`projectID`,
					`evaluationDescription`,
					`bidItemID`,
					`evaluationCreated`,
					`evaluationCreatedByID`
					) 
					VALUES
					(
					:evaluationUUID,
					:projectID,
					:description,
					:bid_item_id,
					UTC_TIMESTAMP,
					:userID
				)");

			    $st->bindValue(':evaluationUUID', Uuid::uuid4()->getBytes());
				$st->bindParam(':projectID', $this->projectID);	 
				$st->bindParam(':description', $this->description);
				$st->bindValue(':bid_item_id', $bid_item_id !== null ? Uuid::fromString($bid_item_id)->getBytes() : null);
				$st->bindParam(':userID', $this->userID);	 
					 
				
				$st->execute();
				
				$evaluationID = $this->db->lastInsertId();
				
				$this->results = [
				    'evaluation_id' => $evaluationID
                ];
				if ($this->guidedBid) {
				    $this->results['bid_item_id'] = $bid_item_id;
                }
				
				if (!empty($this->fileName) && !empty($this->fileContent)) {

                    $path_documents = Path::get('evaluationDocument', $this->companyID, $this->projectID, $evaluationID);
                    if (!is_dir($path_documents) && !mkdir($path_documents, 0755, true)) {
                        throw new AppException('Unable to create evaluation documents directory: %s', $path_documents);
                    }

                    $fileName = File::sanitizeName($this->fileName);
					$filePath = $path_documents . $fileName;
				
     				if (!move_uploaded_file($this->fileContent, $filePath)) {
     				    throw new AppException('Unable to move file: %s', $filePath);
                    }
					
					$fileSt = $this->db->prepare("UPDATE `evaluation`
					SET	
			
					`customEvaluation` = :fileName
					
					WHERE evaluationID = :evaluationID");
				
					$fileSt->bindParam(':evaluationID', $evaluationID);	 
					$fileSt->bindParam(':fileName', $fileName);
					
					$fileSt->execute();

				}

                //Evaluation Bid
                $stTwo = $this->db->prepare("INSERT INTO `customBid`

						(evaluationID, isBidCreated, bidID, bidAcceptanceAmount, bidAcceptanceSplit, bidAcceptanceDue, bidAcceptanceNumber, projectStartAmount, projectStartSplit, projectStartDue, projectStartNumber, projectCompleteAmount, projectCompleteSplit, projectCompleteDue, projectCompleteNumber, bidTotal, bidFirstSent, bidFirstSentByID, contractID, bidLastSent, bidLastViewed, bidAccepted, bidRejected) 
						VALUES 
						(:evaluationID, '1', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null)
						");

                $stTwo->bindParam(':evaluationID', $evaluationID);

                $stTwo->execute();
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>