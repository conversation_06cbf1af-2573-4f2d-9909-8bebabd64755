<?php

	
	class CompanyPhone
	 {
		
		private $db;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setCompany($companyID) {
			$this->companyID = $companyID;
		}
			
			
		public function getPhone() {
			
			if (!empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT `companyPhoneID`, `companyID`, `phoneNumber`, `isPrimary`, `description` AS 'phoneDescription'
 
                FROM 
	
				companyPhones WHERE companyID = :companyID AND deletedAt IS NULL");
				
				//write parameter query to avoid sql injections
				$st->bindParam("companyID", $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCompany[] = $row;
						
						$this->results = $returnCompany; 
						
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>