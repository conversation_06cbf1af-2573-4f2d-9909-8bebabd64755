<?php

	
	class Services {
		
		private $db;
		private $companyID;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setCompany($companyID) {
			$this->companyID = $companyID;
		}
			
			
			
		public function getCompany() {
			
			if (!empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT 
					companyServiceDescription.bidIntroDescription,

					companyServiceDescription.pieringDescription, 
					companyServiceDescription.groutFootingDescription, 
					companyServiceDescription.wallRepairDescription, 
					companyServiceDescription.leaningWallDescription, 
					companyServiceDescription.bowingWallDescription, 
					companyServiceDescription.wallBraceDescription, 
					companyServiceDescription.carbonFiberDescription, 
					companyServiceDescription.wallStiffenerDescription, 
					companyServiceDescription.wallAnchorDescription, 
					companyServiceDescription.wallExcavationDescription,
					companyServiceDescription.wallStraighteningDescription,
					companyServiceDescription.wallGravelBackfillDescription,
					companyServiceDescription.beamPocketDescription, 
					companyServiceDescription.windowWellReplaceDescription, 
					companyServiceDescription.waterManagementDescription, 
					companyServiceDescription.sumpPumpDescription, 
					companyServiceDescription.standardSumpPumpDescription, 
					companyServiceDescription.interiorDrainBasementDescription, 
					companyServiceDescription.interiorDrainCrawlspaceDescription, 
					companyServiceDescription.gutterDischargeDescription, 
					companyServiceDescription.frenchDrainDescription, 
					companyServiceDescription.drainInletDescription, 
					companyServiceDescription.curtainDrainDescription, 
					companyServiceDescription.windowWellDrainDescription, 
					companyServiceDescription.exteriorGradingDescription, 
					companyServiceDescription.supportPostDescription, 
					companyServiceDescription.crackRepairDescription, 
					companyServiceDescription.mudjackingDescription,
					companyServiceDescription.polyurethaneFoamDescription

 				FROM companies 
				LEFT JOIN companyServiceDescription ON companyServiceDescription.companyID = companies.companyID
				
				WHERE companies.companyID=? LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCompany = $row;
						
					}

					
					$this->results = $returnCompany;
				} 
			
			}	
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>