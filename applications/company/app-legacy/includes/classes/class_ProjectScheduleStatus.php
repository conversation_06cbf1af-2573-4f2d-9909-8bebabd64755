<?php


use Core\Components\DB\StaticAccessors\DB;

class ProjectScheduleStatus {
		
		private $db;
		private $projectID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProject($projectID) {
			$this->projectID = $projectID;
		}
			
			
		public function getProject() {
			
			if (!empty($this->projectID)) {

				
				$st = $this->db->prepare("SELECT projectScheduleID, scheduleType, scheduledStart

				FROM projectSchedule 

				WHERE cancelledAt IS NULL AND deletedAt IS NULL AND projectID=?");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->projectID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$allSchedule[] = $row;
						
						$this->results = $allSchedule; 
					
					}
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>