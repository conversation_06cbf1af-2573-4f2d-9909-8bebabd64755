<?php


use Core\Components\DB\StaticAccessors\DB;

class Notes {
		
		private $db;
		private $projectID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProject($projectID, $companyID) {
			$this->projectID = $projectID;
			$this->companyID = $companyID;
		}
			
			
		public function getProject() {
			
			if (!empty($this->projectID) && !empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT n.noteID, n.projectID, n.tiedID, n.note, n.createdAt AS noteAdded, n.createdByUserID AS noteAddedByID, a.userFirstName AS 'noteAddedFirstName', a.userLastName AS 'noteAddedLastName', n.updatedAt AS noteEdited, n.updatedByUserID AS noteEditedByID, e.userFirstName AS 'noteEditedFirstName', e.userLastName AS 'noteEditedLastName', n.noteTag, n.isPinned, v.evaluationDescription
				
				FROM projectNote AS n
				
				LEFT JOIN user AS a ON a.userID = n.createdByUserID
				LEFT JOIN user AS e ON e.userID = n.updatedByUserID 

				LEFT JOIN project AS p on p.projectID = n.projectID
				LEFT JOIN customer AS c ON c.customerID = p.customerID

				LEFT JOIN evaluation AS v ON v.evaluationID = n.tiedID
				
				WHERE n.projectID = :projectID AND p.deletedAt IS NULL AND c.companyID = :companyID AND n.deletedAt IS NULL ORDER BY n.createdAt DESC");
				//write parameter query to avoid sql injections
				$st->bindParam('projectID', $this->projectID);
				$st->bindParam('companyID', $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnNotes[] = $row;
						
					}
					
					$this->results = $returnNotes; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>