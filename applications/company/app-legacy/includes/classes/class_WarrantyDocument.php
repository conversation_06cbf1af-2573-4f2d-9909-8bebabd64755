<?php
	
	
	class Warranty {
		
		private $db;
		private $companyID;
		private $warrantyID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setWarranty($companyID, $warrantyID) {
			$this->companyID = $companyID;
			$this->warrantyID = $warrantyID;
		}
			
			
		public function getWarranty() {
			
			if (!empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT * FROM `warranty` WHERE `companyID` = :companyID AND warrantyID = :warrantyID AND isDELETE IS NULL LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam('companyID', $this->companyID);
				$st->bindParam('warrantyID', $this->warrantyID);
				
				$st->execute();
				
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnWarranty[] = $row;
						
						$this->results = $returnWarranty; 
						
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	
	
?>