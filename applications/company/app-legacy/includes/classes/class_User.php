<?php

	
	class User {
		
		private $db;
		private $userID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
			
		public function setUser($userID) {
			$this->userID = $userID;
		}	
			
			
		public function getUser() {
			
			if (!empty($this->userID)) {
				
				
				$st = $this->db->prepare("SELECT 

					u.userID, 
					u.companyID, 
					u.userFirstName, 
					u.userLastName, 
					u.userPhoneDirect, 
					u.userPhoneCell, 
					u.userEmail, 
					u.admin, 
					u.primary, 
					u.projectManagement,
					u.marketing, 
					u.sales, 
					u.installation, 
					u.bidVerification, 
					u.bidCreation, 
					u.pierDataRecorder,
					u.timecardApprover, 
					u.metrics,
					u.calendarBgColor, 
					u.imageFileID, 
					u.userBio, 
					u.isGoogleCalendar,

					c.name,
					c.emailAddCustomer,
					c.emailAddCustomerLastUpdated,
					c.emailSchedule,
					c.emailScheduleLastUpdated,
					c.scheduleEmailSendSales,
					c.emailBidSent,
					c.emailBidSentLastUpdated,
					c.bidEmailSendSales,
					c.emailInstallation,
					c.emailInstallationLastUpdated,
					c.emailBidAccept,
					c.emailBidAcceptLastUpdated,
					c.bidAcceptEmailSendSales,
					c.emailBidReject,
					c.emailBidRejectLastUpdated,
					c.bidRejectEmailSendSales,
					c.emailFinalPacket,
					c.emailFinalPacketLastUpdated,
					c.emailInvoice,
					c.emailInvoiceLastUpdated,
					c.defaultInvoices,
					c.invoiceSplitBidAcceptance,
					c.invoiceSplitProjectComplete,
					c.timezone, 
					c.daylightSavings,
					c.recentlyCompletedStatus,
					c.latitude,  
					c.longitude,
					c.address,
					c.address2,
					c.city,
					c.state,
					c.zip,
					c.color
				
					
					FROM user AS u
					LEFT JOIN companies AS c on u.companyID = c.companyID

					WHERE u.userID = :userID AND u.userActive = '1' LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam(':userID', $this->userID);		
							
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnUser = $row;
						
						$this->results = $returnUser; 
						
					}
					
				} 
				
			} 
		}

		//gets inactive users as well
		public function getAllUser() {
			
			if (!empty($this->userID)) {
				
				
				$st = $this->db->prepare("SELECT 

					u.userID, 
					u.companyID, 
					u.userFirstName, 
					u.userLastName, 
					u.userPhoneDirect, 
					u.userPhoneCell, 
					u.userEmail, 
					u.primary, 
					u.projectManagement,
					u.marketing, 
					u.sales, 
					u.installation, 
					u.bidVerification, 
					u.bidCreation, 
					u.pierDataRecorder, 
					u.calendarBgColor, 
					u.imageFileID, 
					u.userBio, 

					c.emailAddCustomer,
					c.emailAddCustomerLastUpdated,
					c.emailSchedule,
					c.emailScheduleLastUpdated,
					c.scheduleEmailSendSales,
					c.emailBidSent,
					c.emailBidSentLastUpdated,
					c.bidEmailSendSales,
					c.emailInstallation,
					c.emailInstallationLastUpdated,
					c.emailBidAccept,
					c.emailBidAcceptLastUpdated,
					c.bidAcceptEmailSendSales,
					c.emailBidReject,
					c.emailBidRejectLastUpdated,
					c.bidRejectEmailSendSales,
					c.emailFinalPacket,
					c.emailFinalPacketLastUpdated,
					c.defaultInvoices,
					c.invoiceSplitBidAcceptance,
					c.invoiceSplitProjectComplete,
					c.timezone, 
					c.daylightSavings

					FROM user AS u
					LEFT JOIN companies AS c on u.companyID = c.companyID

					WHERE userID = :userID LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam(':userID', $this->userID);		
							
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnUser = $row;
						
						$this->results = $returnUser; 
						
					}
					
				} 
				
			} 
		}
		
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>