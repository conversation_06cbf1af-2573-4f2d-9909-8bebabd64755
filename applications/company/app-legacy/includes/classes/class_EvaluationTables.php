<?php

	
	class Evaluation {
		
		private $db;
    private $companyID;
		private $evaluationID;
		private $customEvaluation;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setProject($companyID, $evaluationID, $customEvaluation) {
			$this->companyID = $companyID;
      $this->evaluationID = $evaluationID;
			$this->customEvaluation = $customEvaluation;
		}
			
			
		public function getEvaluation() {
			// fxlratr-177: added b.bidAcceptanceName & b.projectCompleteName
			
			if (!empty($this->evaluationID)) {

				if (empty($this->customEvaluation)) {
				
					$st = $this->db->prepare("SELECT 

					e.evaluationID,
					e.projectID,
					e.evaluationDescription,
					e.customEvaluation,
					e.bidItemID,
					e.isPiering,
					e.isWallRepair,
					e.isWaterManagement,
					e.isSupportPosts,
					e.supportPostNotes,
					e.isCrackRepair,
					e.isMudjacking,
					e.isPolyurethaneFoam,
					e.frontFacingDirection,
					e.isStructureAttached,
					e.StructureAttachedDescription,
					e.generalFoundationMaterial,
					e.isWalkOutBasement,
					e.evaluationCreated,
					e.evaluationCreatedByID,
					e.evaluationLastUpdated,
					e.evaluationLastUpdatedByID,
					e.isSendToEngineer,
					e.evaluationCancelled,
					e.evaluationCancelledByID,
					e.evaluationFinalized,
					e.evaluationFinalizedByID,
					e.finalReportSent,
					e.finalReportSentByID,

					p.pieringDataNotes,
					p.isPieringNorth,
					p.isPieringWest,
					p.isPieringSouth,
					p.isPieringEast,
					p.isExistingPiersNorth,
					p.isExistingPiersWest,
					p.isExistingPiersSouth,
					p.isExistingPiersEast,
					p.existingPierCostNorth,
					p.existingPierCostWest,
					p.existingPierCostSouth,
					p.existingPierCostEast,
					p.existingPierNotesNorth,
					p.existingPierNotesWest,
					p.existingPierNotesSouth,
					p.existingPierNotesEast,
					p.isGroutRequiredNorth,
					p.isGroutRequiredWest,
					p.isGroutRequiredSouth,
					p.isGroutRequiredEast,
					p.groutTotalNorth,
					p.groutTotalWest,
					p.groutTotalSouth,
					p.groutTotalEast,
					p.groutBasementNorth,
					p.groutBasementWest,
					p.groutBasementSouth,
					p.groutBasementEast,
					p.groutCrawlspaceNorth,
					p.groutCrawlspaceWest,
					p.groutCrawlspaceSouth,
					p.groutCrawlspaceEast,
					p.groutGarageNorth,
					p.groutGarageWest,
					p.groutGarageSouth,
					p.groutGarageEast,
					p.groutAdditionNorth,
					p.groutAdditionWest,
					p.groutAdditionSouth,
					p.groutAdditionEast,
					p.groutSlabFootingsNorth,
					p.groutSlabFootingsWest,
					p.groutSlabFootingsSouth,
					p.groutSlabFootingsEast,
					p.groutOtherNorth,
					p.groutOtherWest,
					p.groutOtherSouth,
					p.groutOtherEast,
					p.groutOtherDescriptionNorth,
					p.groutOtherDescriptionWest,
					p.groutOtherDescriptionSouth,
					p.groutOtherDescriptionEast,
					p.groutCostNorth,
					p.groutCostWest,
					p.groutCostSouth,
					p.groutCostEast,
					p.groutNotesNorth,
					p.groutNotesWest,
					p.groutNotesSouth,
					p.groutNotesEast,
					p.isPieringObstructionsNorth,
					p.isPieringObstructionsWest,
					p.isPieringObstructionsSouth,
					p.isPieringObstructionsEast,
					p.pieringObstructionsNotesNorth,
					p.pieringObstructionsNotesWest,
					p.pieringObstructionsNotesSouth,
					p.pieringObstructionsNotesEast,
					p.ispieringEquipmentAccessNorth,
					p.ispieringEquipmentAccessWest,
					p.ispieringEquipmentAccessSouth,
					p.ispieringEquipmentAccessEast,
					p.pieringEquipmentAccessCostNorth,
					p.pieringEquipmentAccessCostWest,
					p.pieringEquipmentAccessCostSouth,
					p.pieringEquipmentAccessCostEast,
					p.pieringEquipmentAccessNotesNorth,
					p.pieringEquipmentAccessNotesWest,
					p.pieringEquipmentAccessNotesSouth,
					p.pieringEquipmentAccessNotesEast,
					p.pieringNotesNorth,
					p.pieringNotesWest,
					p.pieringNotesSouth,
					p.pieringNotesEast,

					r.floorJoistOrientation,
					r.floorJoistMeasurement,
					r.isWallRepairNorth,
					r.isWallRepairWest,
					r.isWallRepairSouth,
					r.isWallRepairEast,
					r.isPreviousRepairsNorth,
					r.isPreviousRepairsWest,
					r.isPreviousRepairsSouth,
					r.isPreviousRepairsEast,
					r.previousRepairsCostNorth,
					r.previousRepairsCostWest,
					r.previousRepairsCostSouth,
					r.previousRepairsCostEast,
					r.isWallLeaningNorth,
					r.isWallLeaningWest,
					r.isWallLeaningSouth,
					r.isWallLeaningEast,
					r.maxInwardLeanNorth,
					r.maxInwardLeanWest,
					r.maxInwardLeanSouth,
					r.maxInwardLeanEast,
					r.isWallBowingNorth,
					r.isWallBowingWest,
					r.isWallBowingSouth,
					r.isWallBowingEast,
					r.maxInwardBowNorth,
					r.maxInwardBowWest,
					r.maxInwardBowSouth,
					r.maxInwardBowEast,
					r.isWallBracesNorth,
					r.isWallBracesWest,
					r.isWallBracesSouth,
					r.isWallBracesEast,
					r.wallBraceProductIDNorth,
					r.wallBraceProductIDWest,
					r.wallBraceProductIDSouth,
					r.wallBraceProductIDEast,
					r.wallBraceQuantityNorth,
					r.wallBraceQuantityWest,
					r.wallBraceQuantitySouth,
					r.wallBraceQuantityEast,
					r.isCarbonFiberNorth,
					r.isCarbonFiberWest,
					r.isCarbonFiberSouth,
					r.isCarbonFiberEast,
					r.carbonFiberProductIDNorth,
					r.carbonFiberProductIDWest,
					r.carbonFiberProductIDSouth,
					r.carbonFiberProductIDEast,
					r.carbonFiberQuantityNorth,
					r.carbonFiberQuantityWest,
					r.carbonFiberQuantitySouth,
					r.carbonFiberQuantityEast,
					r.isWallStiffenerNorth,
					r.isWallStiffenerWest,
					r.isWallStiffenerSouth,
					r.isWallStiffenerEast,
					r.wallStiffenerProductIDNorth,
					r.wallStiffenerProductIDWest,
					r.wallStiffenerProductIDSouth,
					r.wallStiffenerProductIDEast,
					r.wallStiffenerQuantityNorth,
					r.wallStiffenerQuantityWest,
					r.wallStiffenerQuantitySouth,
					r.wallStiffenerQuantityEast,
					r.isWallAnchorNorth,
					r.isWallAnchorWest,
					r.isWallAnchorSouth,
					r.isWallAnchorEast,
					r.wallAnchorProductIdNorth,
					r.wallAnchorProductIdWest,
					r.wallAnchorProductIdSouth,
					r.wallAnchorProductIdEast,
					r.wallAnchorQuantityNorth,
					r.wallAnchorQuantityWest,
					r.wallAnchorQuantitySouth,
					r.wallAnchorQuantityEast,
					r.isWallExcavationNorth,
					r.isWallExcavationWest,
					r.isWallExcavationSouth,
					r.isWallExcavationEast,
					r.wallExcavationLengthNorth,
					r.wallExcavationLengthWest,
					r.wallExcavationLengthSouth,
					r.wallExcavationLengthEast,
					r.wallExcavationDepthNorth,
					r.wallExcavationDepthWest,
					r.wallExcavationDepthSouth,
					r.wallExcavationDepthEast,
					r.isWallExcavationTypeNorth,
					r.isWallExcavationTypeWest,
					r.isWallExcavationTypeSouth,
					r.isWallExcavationTypeEast,
					r.wallExcavationStraightenNorth,
					r.wallExcavationStraightenWest,
					r.wallExcavationStraightenSouth,
					r.wallExcavationStraightenEast,
					r.wallExcavationTileDrainProductIDNorth,
					r.wallExcavationTileDrainProductIDWest,
					r.wallExcavationTileDrainProductIDSouth,
					r.wallExcavationTileDrainProductIDEast,
					r.wallExcavationMembraneProductIDNorth,
					r.wallExcavationMembraneProductIDWest,
					r.wallExcavationMembraneProductIDSouth,
					r.wallExcavationMembraneProductIDEast,
					r.wallExcavationGravelBackfillHeightNorth,
					r.wallExcavationGravelBackfillHeightWest,
					r.wallExcavationGravelBackfillHeightSouth,
					r.wallExcavationGravelBackfillHeightEast,
					r.wallExcavationGravelBackfillYardsNorth,
					r.wallExcavationGravelBackfillYardsWest,
					r.wallExcavationGravelBackfillYardsSouth,
					r.wallExcavationGravelBackfillYardsEast,
					r.wallExcavationExcessSoilYardsNorth,
					r.wallExcavationExcessSoilYardsWest,
					r.wallExcavationExcessSoilYardsSouth,
					r.wallExcavationExcessSoilYardsEast,
					r.isRepairBeamPocketsNorth,
					r.isRepairBeamPocketsWest,
					r.isRepairBeamPocketsSouth,
					r.isRepairBeamPocketsEast,
					r.repairBeamPocketsQuantityNorth,
					r.repairBeamPocketsQuantityWest,
					r.repairBeamPocketsQuantitySouth,
					r.repairBeamPocketsQuantityEast,
					r.repairBeamPocketsProductIdNorth,
					r.repairBeamPocketsProductIdWest,
					r.repairBeamPocketsProductIdSouth,
					r.repairBeamPocketsProductIdEast,
					r.isReplaceWindowWellsNorth,
					r.isReplaceWindowWellsWest,
					r.isReplaceWindowWellsSouth,
					r.isReplaceWindowWellsEast,
					r.replaceWindowWellsQuantityNorth,
					r.replaceWindowWellsQuantityWest,
					r.replaceWindowWellsQuantitySouth,
					r.replaceWindowWellsQuantityEast,
					r.replaceWindowWellsProductIdNorth,
					r.replaceWindowWellsProductIdWest,
					r.replaceWindowWellsProductIdSouth,
					r.replaceWindowWellsProductIdEast,
					r.isObstructionNorth,
					r.isObstructionWest,
					r.isObstructionSouth,
					r.isObstructionEast,
					r.isACUnitMoveRequiredNorth,
					r.isACUnitMoveRequiredWest,
					r.isACUnitMoveRequiredSouth,
					r.isACUnitMoveRequiredEast,
					r.aCUnitMoveCostNorth,
					r.aCUnitMoveCostWest,
					r.aCUnitMoveCostSouth,
					r.aCUnitMoveCostEast,
					r.iswallRepairEquipmentAccessNorth,
					r.iswallRepairEquipmentAccessWest,
					r.iswallRepairEquipmentAccessSouth,
					r.iswallRepairEquipmentAccessEast,
					r.wallRepairEquipmentAccessCostNorth,
					r.wallRepairEquipmentAccessCostWest,
					r.wallRepairEquipmentAccessCostSouth,
					r.wallRepairEquipmentAccessCostEast,
					
					wallNotes.previousRepairsNotesNorth,
					wallNotes.previousRepairsNotesWest,
					wallNotes.previousRepairsNotesSouth,
					wallNotes.previousRepairsNotesEast,
					wallNotes.wallExcavationNotesNorth,
					wallNotes.wallExcavationNotesWest,
					wallNotes.wallExcavationNotesSouth,
					wallNotes.wallExcavationNotesEast,
					wallNotes.obstructionNotesNorth,
					wallNotes.obstructionNotesWest,
					wallNotes.obstructionNotesSouth,
					wallNotes.obstructionNotesEast,
					wallNotes.wallRepairEquipmentAccessNotesNorth,
					wallNotes.wallRepairEquipmentAccessNotesWest,
					wallNotes.wallRepairEquipmentAccessNotesSouth,
					wallNotes.wallRepairEquipmentAccessNotesEast,
					wallNotes.notesNorth,
					wallNotes.notesWest,
					wallNotes.notesSouth,
					wallNotes.notesEast,

					c.isFloorCracks,
					c.isWallCracksNorth,
					c.isWallCracksWest,
					c.isWallCracksSouth,
					c.isWallCracksEast,
					c.isWallCrackRepairNorth,
					c.isWallCrackRepairWest,
					c.isWallCrackRepairSouth,
					c.isWallCrackRepairEast,
					c.isCrackObstructionNorth,
					c.isCrackObstructionWest,
					c.isCrackObstructionSouth,
					c.isCrackObstructionEast,
					c.crackObstructionNotesNorth,
					c.crackObstructionNotesWest,
					c.crackObstructionNotesSouth,
					c.crackObstructionNotesEast,
					c.isCrackEquipmentAccessNorth,
					c.isCrackEquipmentAccessWest,
					c.isCrackEquipmentAccessSouth,
					c.isCrackEquipmentAccessEast,
					c.crackEquipmentAccessCostNorth,
					c.crackEquipmentAccessCostWest,
					c.crackEquipmentAccessCostSouth,
					c.crackEquipmentAccessCostEast,
					c.crackEquipmentAccessNotesNorth,
					c.crackEquipmentAccessNotesWest,
					c.crackEquipmentAccessNotesSouth,
					c.crackEquipmentAccessNotesEast,
					c.floorCrackNotes,
					c.crackNotesNorth,
					c.crackNotesWest,
					c.crackNotesSouth,
					c.crackNotesEast,

					w.isSumpPump,
					w.isWaterNorth,
					w.isWaterWest,
					w.isWaterSouth,
					w.isWaterEast,
					w.isInteriorDrainNorth,
					w.isInteriorDrainWest,
					w.isInteriorDrainSouth,
					w.isInteriorDrainEast,
					w.isInteriorDrainTypeNorth, 
					w.isInteriorDrainTypeWest, 
					w.isInteriorDrainTypeSouth, 
					w.isInteriorDrainTypeEast, 
					w.interiorDrainLengthNorth,
					w.interiorDrainLengthWest,
					w.interiorDrainLengthSouth,
					w.interiorDrainLengthEast,
					w.interiorDrainProductIDNorth,
					w.interiorDrainProductIDWest,
					w.interiorDrainProductIDSouth,
					w.interiorDrainProductIDEast,
					w.interiorDrainQuantityNorth,
					w.interiorDrainQuantityWest,
					w.interiorDrainQuantitySouth,
					w.interiorDrainQuantityEast,
					w.isGutterDischargeNorth,
					w.isGutterDischargeWest,
					w.isGutterDischargeSouth,
					w.isGutterDischargeEast,
					w.gutterDischargeLengthNorth,
					w.gutterDischargeLengthWest,
					w.gutterDischargeLengthSouth,
					w.gutterDischargeLengthEast,
					w.gutterDischargeLengthBuriedNorth,
					w.gutterDischargeLengthBuriedWest,
					w.gutterDischargeLengthBuriedSouth,
					w.gutterDischargeLengthBuriedEast,
					w.isFrenchDrainNorth,
					w.isFrenchDrainWest,
					w.isFrenchDrainSouth,
					w.isFrenchDrainEast,
					w.frenchDrainPerforatedLengthNorth,
					w.frenchDrainPerforatedLengthWest,
					w.frenchDrainPerforatedLengthSouth,
					w.frenchDrainPerforatedLengthEast,
					w.frenchDrainNonPerforatedLengthNorth,
					w.frenchDrainNonPerforatedLengthWest,
					w.frenchDrainNonPerforatedLengthSouth,
					w.frenchDrainNonPerforatedLengthEast,
					w.isDrainInletsNorth,
					w.isDrainInletsWest,
					w.isDrainInletsSouth,
					w.isDrainInletsEast,
					w.drainInletsProductIDNorth,
					w.drainInletsProductIDWest,
					w.drainInletsProductIDSouth,
					w.drainInletsProductIDEast,
					w.drainInletsQuantityNorth,
					w.drainInletsQuantityWest,
					w.drainInletsQuantitySouth,
					w.drainInletsQuantityEast,
					w.isCurtainDrainsNorth,
					w.isCurtainDrainsWest,
					w.isCurtainDrainsSouth,
					w.isCurtainDrainsEast,
					w.curtainDrainsLengthNorth,
					w.curtainDrainsLengthWest,
					w.curtainDrainsLengthSouth,
					w.curtainDrainsLengthEast,
					w.isWindowWellNorth,
					w.isWindowWellWest,
					w.isWindowWellSouth,
					w.isWindowWellEast,
					w.windowWellQuantityNorth,
					w.windowWellQuantityWest,
					w.windowWellQuantitySouth,
					w.windowWellQuantityEast,
					w.windowWellInteriorLengthNorth,
					w.windowWellInteriorLengthWest,
					w.windowWellInteriorLengthSouth,
					w.windowWellInteriorLengthEast,
					w.windowWellExteriorLengthNorth,
					w.windowWellExteriorLengthWest,
					w.windowWellExteriorLengthSouth,
					w.windowWellExteriorLengthEast,
					w.isExteriorGradingNorth,
					w.isExteriorGradingWest,
					w.isExteriorGradingSouth,
					w.isExteriorGradingEast,
					w.exteriorGradingHeightNorth,
					w.exteriorGradingHeightWest,
					w.exteriorGradingHeightSouth,
					w.exteriorGradingHeightEast,
					w.exteriorGradingWidthNorth,
					w.exteriorGradingWidthWest,
					w.exteriorGradingWidthSouth,
					w.exteriorGradingWidthEast,
					w.exteriorGradingLengthNorth,
					w.exteriorGradingLengthWest,
					w.exteriorGradingLengthSouth,
					w.exteriorGradingLengthEast,
					w.exteriorGradingYardsNorth,
					w.exteriorGradingYardsWest,
					w.exteriorGradingYardsSouth,
					w.exteriorGradingYardsEast,
					w.isWaterObstructionNorth,
					w.isWaterObstructionWest,
					w.isWaterObstructionSouth,
					w.isWaterObstructionEast,
					w.isWaterACUnitMoveRequiredNorth,
					w.isWaterACUnitMoveRequiredWest,
					w.isWaterACUnitMoveRequiredSouth,
					w.isWaterACUnitMoveRequiredEast,
					w.isWaterACUnitDisconnectedNorth,
					w.isWaterACUnitDisconnectedWest,
					w.isWaterACUnitDisconnectedSouth,
					w.isWaterACUnitDisconnectedEast,
					w.isWaterEquipmentAccessNorth,
					w.isWaterEquipmentAccessWest,
					w.isWaterEquipmentAccessSouth,
					w.isWaterEquipmentAccessEast,
					w.waterEquipmentAccessCostNorth,
					w.waterEquipmentAccessCostWest,
					w.waterEquipmentAccessCostSouth,
					w.waterEquipmentAccessCostEast,
					
					waterNotes.sumpPumpNotes,
					waterNotes.interiorDrainNotesNorth,
					waterNotes.interiorDrainNotesWest,
					waterNotes.interiorDrainNotesSouth,
					waterNotes.interiorDrainNotesEast,
					waterNotes.gutterDischargeNotesNorth,
					waterNotes.gutterDischargeNotesWest,
					waterNotes.gutterDischargeNotesSouth,
					waterNotes.gutterDischargeNotesEast,
					waterNotes.drainInletsNotesNorth,
					waterNotes.drainInletsNotesWest,
					waterNotes.drainInletsNotesSouth,
					waterNotes.drainInletsNotesEast,
					waterNotes.curtainDrainsNotesNorth,
					waterNotes.curtainDrainsNotesWest,
					waterNotes.curtainDrainsNotesSouth,
					waterNotes.curtainDrainsNotesEast,
					waterNotes.windowWellNotesNorth,
					waterNotes.windowWellNotesWest,
					waterNotes.windowWellNotesSouth,
					waterNotes.windowWellNotesEast,
					waterNotes.exteriorGradingNotesNorth,
					waterNotes.exteriorGradingNotesWest,
					waterNotes.exteriorGradingNotesSouth,
					waterNotes.exteriorGradingNotesEast,
					waterNotes.waterObstructionNotesNorth,
					waterNotes.waterObstructionNotesWest,
					waterNotes.waterObstructionNotesSouth,
					waterNotes.waterObstructionNotesEast,
					waterNotes.waterEquipmentAccessNotesNorth,
					waterNotes.waterEquipmentAccessNotesWest,
					waterNotes.waterEquipmentAccessNotesSouth,
					waterNotes.waterEquipmentAccessNotesEast,
					waterNotes.waterNotesNorth,
					waterNotes.waterNotesWest,
					waterNotes.waterNotesSouth,
					waterNotes.waterNotesEast,

					b.bidNumber,
					b.piers,
					b.piersCustom,
					b.existingPiersNorth,
					b.existingPiersNorthCustom,
					b.existingPiersWest,
					b.existingPiersWestCustom,
					b.existingPiersSouth,
					b.existingPiersSouthCustom,
					b.existingPiersEast,
					b.existingPiersEastCustom,
					b.pieringGroutNorth,
					b.pieringGroutNorthCustom,
					b.pieringGroutWest,
					b.pieringGroutWestCustom,
					b.pieringGroutSouth,
					b.pieringGroutSouthCustom,
					b.pieringGroutEast,
					b.pieringGroutEastCustom,
					b.previousWallRepairNorth,
					b.previousWallRepairNorthCustom,
					b.previousWallRepairWest,
					b.previousWallRepairWestCustom,
					b.previousWallRepairSouth,
					b.previousWallRepairSouthCustom,
					b.previousWallRepairEast,
					b.previousWallRepairEastCustom,
					b.wallBracesNorth,
					b.wallBracesNorthCustom,
					b.wallBracesWest,
					b.wallBracesWestCustom,
					b.wallBracesSouth,
					b.wallBracesSouthCustom,
					b.wallBracesEast,
					b.wallBracesEastCustom,
					b.carbonFiberNorth,
					b.carbonFiberNorthCustom,
					b.carbonFiberWest,
					b.carbonFiberWestCustom,
					b.carbonFiberSouth,
					b.carbonFiberSouthCustom,
					b.carbonFiberEast,
					b.carbonFiberEastCustom,
					b.wallStiffenerNorth,
					b.wallStiffenerNorthCustom,
					b.wallStiffenerWest,
					b.wallStiffenerWestCustom,
					b.wallStiffenerSouth,
					b.wallStiffenerSouthCustom,
					b.wallStiffenerEast,
					b.wallStiffenerEastCustom,
					b.wallAnchorsNorth,
					b.wallAnchorsNorthCustom,
					b.wallAnchorsWest,
					b.wallAnchorsWestCustom,
					b.wallAnchorsSouth,
					b.wallAnchorsSouthCustom,
					b.wallAnchorsEast,
					b.wallAnchorsEastCustom,
					b.wallExcavationNorth,
					b.wallExcavationNorthCustom,
					b.wallExcavationWest,
					b.wallExcavationWestCustom,
					b.wallExcavationSouth,
					b.wallExcavationSouthCustom,
					b.wallExcavationEast,
					b.wallExcavationEastCustom,
					b.beamPocketsNorth,
					b.beamPocketsNorthCustom,
					b.beamPocketsWest,
					b.beamPocketsWestCustom,
					b.beamPocketsSouth,
					b.beamPocketsSouthCustom,
					b.beamPocketsEast,
					b.beamPocketsEastCustom,
					b.windowWellReplacedNorth,
					b.windowWellReplacedNorthCustom,
					b.windowWellReplacedWest,
					b.windowWellReplacedWestCustom,
					b.windowWellReplacedSouth,
					b.windowWellReplacedSouthCustom,
					b.windowWellReplacedEast,
					b.windowWellReplacedEastCustom,
					b.sumpPump,
					b.sumpPumpCustom,
					b.interiorDrainNorth,
					b.interiorDrainNorthCustom,
					b.interiorDrainWest,
					b.interiorDrainWestCustom,
					b.interiorDrainSouth,
					b.interiorDrainSouthCustom,
					b.interiorDrainEast,
					b.interiorDrainEastCustom,
					b.gutterDischargeNorth,
					b.gutterDischargeNorthCustom,
					b.gutterDischargeWest,
					b.gutterDischargeWestCustom,
					b.gutterDischargeSouth,
					b.gutterDischargeSouthCustom,
					b.gutterDischargeEast,
					b.gutterDischargeEastCustom,
					b.frenchDrainNorth,
					b.frenchDrainNorthCustom,
					b.frenchDrainWest,
					b.frenchDrainWestCustom,
					b.frenchDrainSouth,
					b.frenchDrainSouthCustom,
					b.frenchDrainEast,
					b.frenchDrainEastCustom,
					b.drainInletsNorth,
					b.drainInletsNorthCustom,
					b.drainInletsWest,
					b.drainInletsWestCustom,
					b.drainInletsSouth,
					b.drainInletsSouthCustom,
					b.drainInletsEast,
					b.drainInletsEastCustom,
					b.curtainDrainsNorth,
					b.curtainDrainsNorthCustom,
					b.curtainDrainsWest,
					b.curtainDrainsWestCustom,
					b.curtainDrainsSouth,
					b.curtainDrainsSouthCustom,
					b.curtainDrainsEast,
					b.curtainDrainsEastCustom,
					b.windowWellDrainsNorth,
					b.windowWellDrainsNorthCustom,
					b.windowWellDrainsWest,
					b.windowWellDrainsWestCustom,
					b.windowWellDrainsSouth,
					b.windowWellDrainsSouthCustom,
					b.windowWellDrainsEast,
					b.windowWellDrainsEastCustom,
					b.exteriorGradingNorth,
					b.exteriorGradingNorthCustom,
					b.exteriorGradingWest,
					b.exteriorGradingWestCustom,
					b.exteriorGradingSouth,
					b.exteriorGradingSouthCustom,
					b.exteriorGradingEast,
					b.exteriorGradingEastCustom,
					b.existingSupportPosts,
					b.existingSupportPostsCustom,
					b.newSupportPosts,
					b.newSupportPostsCustom,
					b.floorCracks,
					b.floorCracksCustom,
					b.wallCracksNorth,
					b.wallCracksNorthCustom,
					b.wallCracksWest,
					b.wallCracksWestCustom,
					b.wallCracksSouth,
					b.wallCracksSouthCustom,
					b.wallCracksEast,
					b.wallCracksEastCustom,
					b.mudjacking,
					b.mudjackingCustom,
					b.polyurethaneFoam,
					b.polyurethaneFoamCustom,
					b.customServices,
					b.otherServices,
					b.pieringObstructionsNorth,
					b.pieringObstructionsWest,
					b.pieringObstructionsSouth,
					b.pieringObstructionsEast,
					b.wallObstructionsNorth,
					b.wallObstructionsWest,
					b.wallObstructionsSouth,
					b.wallObstructionsEast,
					b.waterObstructionsNorth,
					b.waterObstructionsWest,
					b.waterObstructionsSouth,
					b.waterObstructionsEast,
					b.crackObstructionsNorth,
					b.crackObstructionsWest,
					b.crackObstructionsSouth,
					b.crackObstructionsEast,
					b.isBidCreated,
					b.bidID,
					b.bidAcceptanceName,
					b.bidAcceptanceAmount,
					b.bidAcceptanceSplit,
					b.bidAcceptanceDue,
					b.bidAcceptanceNumber,
					b.projectStartAmount,
					b.projectStartSplit,
					b.projectStartDue,
					b.projectStartNumber,
					b.projectCompleteName,
					b.projectCompleteAmount,
					b.projectCompleteSplit,
					b.projectCompleteDue,
					b.projectCompleteNumber,
					b.bidSubTotal,
					b.bidTotal,
					b.bidDiscount,
					b.bidDiscountType,
					b.bidFirstSent,
					b.bidFirstSentByID,
					b.contractID,
					b.bidLastSent,
					b.bidLastViewed,
					b.bidAccepted,
					b.bidAcceptedName,
					b.bidRejected,

					northWallBrace.pricingWallBracesID,
					northWallBrace.name AS northWallBraceName,
					northWallBrace.description AS northWallBraceDescription,
					westWallBrace.pricingWallBracesID,
					westWallBrace.name AS westWallBraceName,
					westWallBrace.description AS westWallBraceDescription,
					southWallBrace.pricingWallBracesID,
					southWallBrace.name AS southWallBraceName,
					southWallBrace.description AS southWallBraceDescription,
					eastWallBrace.pricingWallBracesID,
					eastWallBrace.name AS eastWallBraceName,
					eastWallBrace.description AS eastWallBraceDescription,
					
					northCarbonFiber.pricingCarbonFiberID,
					northCarbonFiber.name AS northCarbonFiberName,
					northCarbonFiber.description AS northCarbonFiberDescription,
					westCarbonFiber.pricingCarbonFiberID,
					westCarbonFiber.name AS westCarbonFiberName,
					westCarbonFiber.description AS westCarbonFiberDescription,
					southCarbonFiber.pricingCarbonFiberID,
					southCarbonFiber.name AS southCarbonFiberName,
					southCarbonFiber.description AS southCarbonFiberDescription,
					eastCarbonFiber.pricingCarbonFiberID,
					eastCarbonFiber.name AS eastCarbonFiberName,
					eastCarbonFiber.description AS eastCarbonFiberDescription,

					northWallStiffener.pricingWallStiffenerID,
					northWallStiffener.name AS northWallStiffenerName,
					northWallStiffener.description AS northWallStiffenerDescription,
					westWallStiffener.pricingWallStiffenerID,
					westWallStiffener.name AS westWallStiffenerName,
					westWallStiffener.description AS westWallStiffenerDescription,
					southWallStiffener.pricingWallStiffenerID,
					southWallStiffener.name AS southWallStiffenerName,
					southWallStiffener.description AS southWallStiffenerDescription,
					eastWallStiffener.pricingWallStiffenerID,
					eastWallStiffener.name AS eastWallStiffenerName,
					eastWallStiffener.description AS eastWallStiffenerDescription,

					northWallAnchor.pricingWallAnchorID,
					northWallAnchor.name AS northWallAnchorName,
					northWallAnchor.description AS northWallAnchorDescription,
					westWallAnchor.pricingWallAnchorID,
					westWallAnchor.name AS westWallAnchorName,
					westWallAnchor.description AS westWallAnchorDescription,
					southWallAnchor.pricingWallAnchorID,
					southWallAnchor.name AS southWallAnchorName,
					southWallAnchor.description AS southWallAnchorDescription,
					eastWallAnchor.pricingWallAnchorID,
					eastWallAnchor.name AS eastWallAnchorName,
					eastWallAnchor.description AS eastWallAnchorDescription,

					northDrainInlet.pricingDrainInletID,
					northDrainInlet.name AS northDrainInletName,
					northDrainInlet.description AS northDrainInletDescription,
					westDrainInlet.pricingDrainInletID,
					westDrainInlet.name AS westDrainInletName,
					westDrainInlet.description AS westDrainInletDescription,
					southDrainInlet.pricingDrainInletID,
					southDrainInlet.name AS southDrainInletName,
					southDrainInlet.description AS southDrainInletDescription,
					eastDrainInlet.pricingDrainInletID,
					eastDrainInlet.name AS eastDrainInletName,
					eastDrainInlet.description AS eastDrainInletDescription,

					northInteriorDrain.pricingInteriorDrainID,
					northInteriorDrain.name AS northInteriorDrainName,
					northInteriorDrain.description AS northInteriorDrainDescription,
					westInteriorDrain.pricingInteriorDrainID,
					westInteriorDrain.name AS westInteriorDrainName,
					westInteriorDrain.description AS westInteriorDrainDescription,
					southInteriorDrain.pricingInteriorDrainID,
					southInteriorDrain.name AS southInteriorDrainName,
					southInteriorDrain.description AS southInteriorDrainDescription,
					eastInteriorDrain.pricingInteriorDrainID,
					eastInteriorDrain.name AS eastInteriorDrainName,
					eastInteriorDrain.description AS eastInteriorDrainDescription,

					northMembrane.pricingMembraneID,
					northMembrane.name AS northMembranesName,
					northMembrane.description AS northMembranesDescription,
					westMembrane.pricingMembraneID,
					westMembrane.name AS westMembranesName,
					westMembrane.description AS westMembranesDescription,
					southMembrane.pricingMembraneID,
					southMembrane.name AS southMembranesName,
					southMembrane.description AS southMembranesDescription,
					eastMembrane.pricingMembraneID,
					eastMembrane.name AS eastMembranesName,
					eastMembrane.description AS eastMembranesDescription,

					northTileDrain.pricingTileDrainID,
					northTileDrain.name AS northTileDrainName,
					northTileDrain.description AS northTileDrainDescription,
					westTileDrain.pricingTileDrainID,
					westTileDrain.name AS westTileDrainName,
					westTileDrain.description AS westTileDrainDescription,
					southTileDrain.pricingTileDrainID,
					southTileDrain.name AS southTileDrainName,
					southTileDrain.description AS southTileDrainDescription,
					eastTileDrain.pricingTileDrainID,
					eastTileDrain.name AS eastTileDrainName,
					eastTileDrain.description AS eastTileDrainDescription

					FROM 
		
					evaluation AS e 
          JOIN project on e.projectID = project.projectID
          JOIN customer on project.customerID = customer.customerID
		
					LEFT JOIN evaluationPiering AS p ON p.evaluationID = e.evaluationID 
					LEFT JOIN evaluationWallRepair AS r ON r.evaluationID = e.evaluationID 
					LEFT JOIN evaluationWallRepairNotes AS wallNotes ON wallNotes.evaluationID = e.evaluationID
					LEFT JOIN evaluationCrack AS c ON c.evaluationID = e.evaluationID
					LEFT JOIN evaluationWater AS w ON w.evaluationID = e.evaluationID
					LEFT JOIN evaluationWaterNotes AS waterNotes ON waterNotes.evaluationID = e.evaluationID
					LEFT JOIN evaluationBid AS b ON b.evaluationID = e.evaluationID

					LEFT JOIN pricingWallBraces AS northWallBrace ON northWallBrace.pricingWallBracesID = r.wallBraceProductIDNorth
					LEFT JOIN pricingWallBraces AS westWallBrace ON westWallBrace.pricingWallBracesID = r.wallBraceProductIDWest
					LEFT JOIN pricingWallBraces AS southWallBrace ON southWallBrace.pricingWallBracesID = r.wallBraceProductIDSouth
					LEFT JOIN pricingWallBraces AS eastWallBrace ON eastWallBrace.pricingWallBracesID = r.wallBraceProductIDEast
					
					LEFT JOIN pricingCarbonFiber AS northCarbonFiber ON northCarbonFiber.pricingCarbonFiberID = r.carbonFiberProductIDNorth
					LEFT JOIN pricingCarbonFiber AS westCarbonFiber ON westCarbonFiber.pricingCarbonFiberID = r.carbonFiberProductIDWest
					LEFT JOIN pricingCarbonFiber AS southCarbonFiber ON southCarbonFiber.pricingCarbonFiberID = r.carbonFiberProductIDSouth
					LEFT JOIN pricingCarbonFiber AS eastCarbonFiber ON eastCarbonFiber.pricingCarbonFiberID = r.carbonFiberProductIDEast

					LEFT JOIN pricingWallStiffener AS northWallStiffener ON northWallStiffener.pricingWallStiffenerID = r.wallStiffenerProductIDNorth
					LEFT JOIN pricingWallStiffener AS westWallStiffener ON westWallStiffener.pricingWallStiffenerID = r.wallStiffenerProductIDWest
					LEFT JOIN pricingWallStiffener AS southWallStiffener ON southWallStiffener.pricingWallStiffenerID = r.wallStiffenerProductIDSouth
					LEFT JOIN pricingWallStiffener AS eastWallStiffener ON eastWallStiffener.pricingWallStiffenerID = r.wallStiffenerProductIDEast

					LEFT JOIN pricingWallAnchor AS northWallAnchor ON northWallAnchor.pricingWallAnchorID = r.wallAnchorProductIdNorth
					LEFT JOIN pricingWallAnchor AS westWallAnchor ON westWallAnchor.pricingWallAnchorID = r.wallAnchorProductIdWest
					LEFT JOIN pricingWallAnchor AS southWallAnchor ON southWallAnchor.pricingWallAnchorID = r.wallAnchorProductIdSouth
					LEFT JOIN pricingWallAnchor AS eastWallAnchor ON eastWallAnchor.pricingWallAnchorID = r.wallAnchorProductIdEast

					LEFT JOIN pricingDrainInlet AS northDrainInlet ON northDrainInlet.pricingDrainInletID = w.drainInletsProductIDNorth
					LEFT JOIN pricingDrainInlet AS westDrainInlet ON westDrainInlet.pricingDrainInletID = w.drainInletsProductIDWest
					LEFT JOIN pricingDrainInlet AS southDrainInlet ON southDrainInlet.pricingDrainInletID = w.drainInletsProductIDSouth
					LEFT JOIN pricingDrainInlet AS eastDrainInlet ON eastDrainInlet.pricingDrainInletID = w.drainInletsProductIDEast

					LEFT JOIN pricingMembrane AS northMembrane ON northMembrane.pricingMembraneID = r.wallExcavationMembraneProductIDNorth
					LEFT JOIN pricingMembrane AS westMembrane ON westMembrane.pricingMembraneID = r.wallExcavationMembraneProductIDWest
					LEFT JOIN pricingMembrane AS southMembrane ON southMembrane.pricingMembraneID = r.wallExcavationMembraneProductIDSouth
					LEFT JOIN pricingMembrane AS eastMembrane ON eastMembrane.pricingMembraneID = r.wallExcavationMembraneProductIDEast

					LEFT JOIN pricingTileDrain AS northTileDrain ON northTileDrain.pricingTileDrainID = r.wallExcavationTileDrainProductIDNorth
					LEFT JOIN pricingTileDrain AS westTileDrain ON westTileDrain.pricingTileDrainID = r.wallExcavationTileDrainProductIDWest
					LEFT JOIN pricingTileDrain AS southTileDrain ON southTileDrain.pricingTileDrainID = r.wallExcavationTileDrainProductIDSouth
					LEFT JOIN pricingTileDrain AS eastTileDrain ON eastTileDrain.pricingTileDrainID = r.wallExcavationTileDrainProductIDEast
					
					LEFT JOIN pricingInteriorDrain AS northInteriorDrain ON northInteriorDrain.pricingInteriorDrainID = w.interiorDrainProductIDNorth
					LEFT JOIN pricingInteriorDrain AS westInteriorDrain ON westInteriorDrain.pricingInteriorDrainID = w.interiorDrainProductIDWest
					LEFT JOIN pricingInteriorDrain AS southInteriorDrain ON southInteriorDrain.pricingInteriorDrainID = w.interiorDrainProductIDSouth
					LEFT JOIN pricingInteriorDrain AS eastInteriorDrain ON eastInteriorDrain.pricingInteriorDrainID = w.interiorDrainProductIDEast

					WHERE customer.companyID=? AND e.evaluationID=? AND e.deletedAt IS NULL LIMIT 1");
					//write parameter query to avoid sql injections
					$st->bindParam(1, $this->companyID);
					$st->bindParam(2, $this->evaluationID);
					
					$st->execute();
					
					if ($st->rowCount()==1) {
						while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
							$returnEvaluation = $row;
							
							
						}
						
						$this->results = $returnEvaluation;
					} 

				} else {

					$st = $this->db->prepare("SELECT 

					e.evaluationID,
					e.projectID,
					e.evaluationDescription,
					e.customEvaluation,
					e.bidItemID,
					e.isPiering,
					e.isWallRepair,
					e.isWaterManagement,
					e.isSupportPosts,
					e.isCrackRepair,
					e.isMudjacking,
					e.isPolyurethaneFoam,
					e.frontFacingDirection,
					e.isStructureAttached,
					e.StructureAttachedDescription,
					e.generalFoundationMaterial,
					e.isWalkOutBasement,
					e.evaluationCreated,
					e.evaluationCreatedByID,
					e.evaluationLastUpdated,
					e.evaluationLastUpdatedByID,
					e.isSendToEngineer,
					e.evaluationCancelled,
					e.evaluationCancelledByID,
					e.evaluationFinalized,
					e.evaluationFinalizedByID,
					e.finalReportSent,
					e.finalReportSentByID,

					b.bidNumber,
					b.isBidCreated,
					b.bidID,
					b.bidAcceptanceName,
					b.bidAcceptanceAmount,
					b.bidAcceptanceSplit,
					b.bidAcceptanceDue,
					b.bidAcceptanceNumber,
					b.invoicePaidAccept,
					b.projectStartAmount,
					b.projectStartSplit,
					b.projectStartDue,
					b.projectStartNumber,
					b.projectCompleteName,
					b.projectCompleteAmount,
					b.projectCompleteSplit,
					b.projectCompleteDue,
					b.projectCompleteNumber,
					b.invoicePaidComplete,
					b.bidTotal,
					b.bidFirstSent,
					b.bidFirstSentByID,
					b.contractID,
					b.bidLastSent,
					b.bidLastViewed,
					b.bidAccepted,
					b.bidAcceptedName,
					b.bidRejected
						
					FROM 
		
					evaluation AS e 
          JOIN project on e.projectID = project.projectID
          JOIN customer on project.customerID = customer.customerID
					LEFT JOIN customBid AS b ON b.evaluationID = e.evaluationID

					WHERE customer.companyID=? AND e.evaluationID=? AND e.deletedAt IS NULL LIMIT 1");
					//write parameter query to avoid sql injections
					$st->bindParam(1, $this->companyID);
					$st->bindParam(2, $this->evaluationID);
					
					$st->execute();
					
					if ($st->rowCount()==1) {
						while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
							$returnEvaluation = $row;
							
							
						}
						
						$this->results = $returnEvaluation;
					} 
				}
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>