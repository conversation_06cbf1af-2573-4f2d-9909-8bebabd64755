<?php


use Common\Models\BidItem;
use Core\Components\DB\StaticAccessors\DB;

class BidCreated {
		
		private $db;
		private $companyID;
		private $sort;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setStatus($companyID, $sort, $groupBy) {
			$this->companyID = $companyID;
			$this->sort = $sort;
            $this->groupBy = $groupBy;
		}
			
		public function setUser($userID) {
			$this->userID = $userID;
		}	
			
		public function getStatus() {
			
			if (!empty($this->companyID)) {
				
				$sqlStatement = "
				(SELECT 'BidCreated' AS statusType, 'true' AS customBid, c.firstName, c.lastName, c.businessName, 
				
				IF(e.evaluationFinalized IS NULL, bidItems.submittedAt, e.evaluationFinalized) AS time,
				
				j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,
				
				IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson',
                user.userFirstName,
                user.userLastName,
                user.userActive,
                IF(bidItems.referenceID IS NOT NULL, bidItems.referenceID, null) as referenceID
                
				FROM 

				customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID
					AND e.evaluationCancelled IS NULL
					AND e.deletedAt IS NULL

				JOIN customBid AS b ON b.evaluationID = e.evaluationID
				LEFT JOIN bidItems ON bidItems.bidItemID = e.bidItemID
				LEFT JOIN user ON user.userID = j.projectSalesperson
				
				WHERE 
				
				(e.customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bidItems.type != :bidItemTypeLegacy)) AND
				(bidItems.submittedAt IS NOT NULL OR e.evaluationFinalized IS NOT NULL) AND
				
				c.deletedAt IS NULL AND
				b.bidFirstSent IS NULL AND
				
				c.companyID = :companyID2)

				ORDER BY";

                if ($this->groupBy) {
                    $sqlStatement = $sqlStatement . ' userLastName ASC, userFirstName ASC,';
                }

                $sqlStatement = $sqlStatement . ' time';

                if ($this->sort == 'asc') {
                    $sqlStatement = $sqlStatement . ' ASC';

                } else {
                    $sqlStatement = $sqlStatement . ' DESC';
                }
				
				$st = $this->db->prepare($sqlStatement); 

                $st->bindParam(':companyID2', $this->companyID);
                $st->bindValue(':bidItemTypeLegacy', BidItem::TYPE_LEGACY);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$notificationArray[] = $row;
						
						$this->results = $notificationArray;
					}
					
				} 

				
			} 
		}

		public function getStatusUser() {
			
			if (!empty($this->companyID) && !empty($this->userID)) {
				
				$sqlStatement = "
				(SELECT 'BidCreated' AS statusType, 'true' AS customBid, c.firstName, c.lastName, c.businessName, 
				
				IF(e.evaluationFinalized IS NULL, bidItems.submittedAt, e.evaluationFinalized) AS time, 
				
				j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,
				
				:userID4 AS 'salesID', IF(bidItems.referenceID IS NOT NULL, bidItems.referenceID, null) as referenceID
				
				FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID
					AND e.evaluationCancelled IS NULL
					AND e.deletedAt IS NULL

				JOIN customBid AS b ON b.evaluationID = e.evaluationID
				LEFT JOIN bidItems ON bidItems.bidItemID = e.bidItemID
				
				WHERE 
				
				(e.customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bidItems.type != :bidItemTypeLegacy)) AND
				(bidItems.submittedAt IS NOT NULL OR e.evaluationFinalized IS NOT NULL) AND
				
				c.deletedAt IS NULL AND
				b.bidFirstSent IS NULL AND

				(j.projectSalesperson = :userID5 OR e.evaluationCreatedByID = :userID6) AND 
				
				c.companyID = :companyID2)

				ORDER BY time";

				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';
					
				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}
				
				$st = $this->db->prepare($sqlStatement); 

                $st->bindParam(':userID4', $this->userID);
                $st->bindParam(':userID5', $this->userID);
                $st->bindParam(':userID6', $this->userID);
                $st->bindParam(':companyID2', $this->companyID);
                $st->bindValue(':bidItemTypeLegacy', BidItem::TYPE_LEGACY);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$notificationArray[] = $row;
						
						$this->results = $notificationArray;
					}
					
				} 

				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>