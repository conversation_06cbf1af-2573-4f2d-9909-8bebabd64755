<?php


use Common\Models\BidItem;
use Core\Components\DB\StaticAccessors\DB;

class FinalReportSent {
		
		private $db;
		private $companyID;
		private $sort;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setStatus($companyID, $sort, $groupBy) {
			$this->companyID = $companyID;
			$this->sort = $sort;
            $this->groupBy = $groupBy;
		}
			
		public function setUser($userID) {
			$this->userID = $userID;
		}		
			
		public function getStatus() {
			
			if (!empty($this->companyID)) {
				
				$sqlStatement = "(SELECT 'FinalReportSent' AS statusType, c.firstName, c.lastName, c.businessName, 
				e.finalReportSent AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip, 
				s.projectScheduleID, s.scheduleType,
				
				IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson',
                user.userFirstName,
                user.userLastName,
                user.userActive
				
				FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID 
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN projectSchedule AS s ON s.projectID = j.projectID 
					AND s.scheduleType = 'Installation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID 
					AND e.customEvaluation IS NULL 
					AND e.evaluationCancelled IS NULL 
					AND e.evaluationFinalized IS NOT NULL
					AND e.deletedAt IS NULL

				JOIN evaluationBid AS b ON b.evaluationID = e.evaluationID	
				LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN user ON user.userID = j.projectSalesperson
				
				WHERE 
				
				c.deletedAt IS NULL AND
				(e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy1) AND 
				e.finalReportSent IS NOT NULL AND
				b.isBidCreated IS NOT NULL AND
				b.bidFirstSent IS NOT NULL AND
				b.bidAccepted IS NOT NULL AND
				s.completedAt IS NOT NULL AND
				
				c.companyID = :companyID1) 

				UNION ALL

				(SELECT 'FinalReportSent' AS statusType, c.firstName, c.lastName, c.businessName, e.finalReportSent AS time, 
				j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip, s.projectScheduleID, s.scheduleType,
				
				IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson',
                user.userFirstName,
                user.userLastName,
                user.userActive
				
				FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID 
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID 
					AND e.evaluationCancelled IS NULL
					AND e.deletedAt IS NULL 

				JOIN customBid AS b ON b.evaluationID = e.evaluationID

				JOIN projectSchedule AS s ON s.projectID = j.projectID 
					AND s.scheduleType = 'Installation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL
					
                LEFT JOIN user ON user.userID = j.projectSalesperson
                LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				(e.customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy2)) AND
				e.finalReportSent IS NOT NULL AND
				b.isBidCreated IS NOT NULL AND
				b.bidFirstSent IS NOT NULL AND
				b.bidAccepted IS NOT NULL AND
				s.completedAt IS NOT NULL AND
				
				c.companyID = :companyID2)

				ORDER BY";

                if ($this->groupBy) {
                    $sqlStatement = $sqlStatement . ' userLastName ASC, userFirstName ASC,';
                }

                $sqlStatement = $sqlStatement . ' time';

                if ($this->sort == 'asc') {
                    $sqlStatement = $sqlStatement . ' ASC';

                } else {
                    $sqlStatement = $sqlStatement . ' DESC';
                }
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':companyID1', $this->companyID);
                $st->bindParam(':companyID2', $this->companyID);
                $st->bindValue(':bidItemTypeLegacy1', BidItem::TYPE_LEGACY);
                $st->bindValue(':bidItemTypeLegacy2', BidItem::TYPE_LEGACY);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$notificationArray[] = $row;
						
						$this->results = $notificationArray;
					}
					
				} 
				
			} 
		}

		public function getStatusUser() {
			
			if (!empty($this->companyID) && !empty($this->userID)) {
				
				$sqlStatement = "(SELECT 'FinalReportSent' AS statusType, c.firstName, c.lastName, c.businessName, 
				e.finalReportSent AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip, 
				s.projectScheduleID, s.scheduleType,
				
				:userID1 AS 'salesID'
				
				FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID 
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN projectSchedule AS s ON s.projectID = j.projectID 
					AND s.scheduleType = 'Installation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID 
					AND e.customEvaluation IS NULL 
					AND e.evaluationCancelled IS NULL 
					AND e.evaluationFinalized IS NOT NULL
					AND e.deletedAt IS NULL

				JOIN evaluationBid AS b ON b.evaluationID = e.evaluationID	
				LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				(e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy1) AND 
				e.finalReportSent IS NOT NULL AND
				b.isBidCreated IS NOT NULL AND
				b.bidFirstSent IS NOT NULL AND
				b.bidAccepted IS NOT NULL AND
				s.completedAt IS NOT NULL AND

				(j.projectSalesperson = :userID2 OR e.evaluationCreatedByID = :userID3) AND 
				
				c.companyID = :companyID1) 

				UNION ALL

				(SELECT 'FinalReportSent' AS statusType, c.firstName, c.lastName, c.businessName, e.finalReportSent AS time, 
				j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip, s.projectScheduleID, s.scheduleType, 
				
				:userID4 AS 'salesID'
				
				FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID 
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN evaluation AS e ON e.projectID = j.projectID 
					AND e.evaluationCancelled IS NULL
					AND e.deletedAt IS NULL 

				JOIN customBid AS b ON b.evaluationID = e.evaluationID
				LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID

				JOIN projectSchedule AS s ON s.projectID = j.projectID 
					AND s.scheduleType = 'Installation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				(e.customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy2)) AND
				e.finalReportSent IS NOT NULL AND
				b.isBidCreated IS NOT NULL AND
				b.bidFirstSent IS NOT NULL AND
				b.bidAccepted IS NOT NULL AND
				s.completedAt IS NOT NULL AND

				(j.projectSalesperson = :userID5 OR e.evaluationCreatedByID = :userID6) AND 
				
				c.companyID = :companyID2)

				ORDER BY time";

				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';
					
				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':userID1', $this->userID);
                $st->bindParam(':userID2', $this->userID);
                $st->bindParam(':userID3', $this->userID);
                $st->bindParam(':userID4', $this->userID);
                $st->bindParam(':userID5', $this->userID);
                $st->bindParam(':userID6', $this->userID);
				$st->bindParam(':companyID1', $this->companyID);
                $st->bindParam(':companyID2', $this->companyID);
                $st->bindValue(':bidItemTypeLegacy1', BidItem::TYPE_LEGACY);
                $st->bindValue(':bidItemTypeLegacy2', BidItem::TYPE_LEGACY);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$notificationArray[] = $row;
						
						$this->results = $notificationArray;
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>