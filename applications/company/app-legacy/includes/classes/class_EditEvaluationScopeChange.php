<?php


use Common\Models\BidItem;

class ScopeChange {
		
		private $db;
		private $evaluationID;
		private $changeTotal;
		private $changeType;
		private $changeID;
		private $changeNumber;
		private $changeQuickbooks;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
		}
			

		public function setEvaluation($evaluationID, $changeTotal, $changeType, $changeID, $changeNumber, $changeQuickbooks) {
			$this->evaluationID = $evaluationID;
			$this->changeTotal = $changeTotal;
			$this->changeType = $changeType;
			$this->changeID = $changeID;
			$this->changeNumber = $changeNumber;
			$this->changeQuickbooks = $changeQuickbooks;
		}
			
			
		public function sendEvaluation() {
			
			if (!empty($this->evaluationID)) {

				$st = $this->db->prepare("SELECT 
                    e.customEvaluation, IF(e.bidItemID IS NOT NULL AND bi.type = :bidItemTypeLegacy, NULL, e.bidItemID) AS bidItemID
                    FROM evaluation AS e
                    LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
                    WHERE e.evaluationID = :evaluationID
                    LIMIT 1");
				$st->bindParam(':evaluationID', $this->evaluationID);
				$st->bindValue(':bidItemTypeLegacy', BidItem::TYPE_LEGACY);
				$st->execute();

				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$customEvaluation = $row['customEvaluation'];
						$bidItemID = $row['bidItemID'];
					}

					if ($customEvaluation == null && $bidItemID == null) {
						$sqlUpdate = $this->db->prepare("UPDATE evaluationBid SET

						`bidScopeChangeTotal` = :changeTotal, 
						`bidScopeChangeType`= :changeType,
						`bidScopeChangeQuickbooksID`= :changeID,
						`bidScopeChangeNumber`= :changeNumber,
						`bidScopeChangeQuickbooks`= :changeQuickbooks

						WHERE evaluationID = :evaluationID");
						
						$sqlUpdate->bindParam(':changeTotal', $this->changeTotal);
						$sqlUpdate->bindParam(':changeType', $this->changeType);
						$sqlUpdate->bindParam(':changeID', $this->changeID);
						$sqlUpdate->bindParam(':changeNumber', $this->changeNumber);
						$sqlUpdate->bindParam(':changeQuickbooks', $this->changeQuickbooks);
						$sqlUpdate->bindParam(':evaluationID', $this->evaluationID);

						if ($sqlUpdate->execute()) {
							$this->results = 'true';
						}

					} else {
						$sqlUpdate = $this->db->prepare("UPDATE customBid SET

						`bidScopeChangeTotal` = :changeTotal, 
						`bidScopeChangeType`= :changeType,
						`bidScopeChangeQuickbooksID`= :changeID,
						`bidScopeChangeNumber`= :changeNumber,
						`bidScopeChangeQuickbooks`= :changeQuickbooks

						WHERE evaluationID = :evaluationID");
						
						$sqlUpdate->bindParam(':changeTotal', $this->changeTotal);
						$sqlUpdate->bindParam(':changeType', $this->changeType);
						$sqlUpdate->bindParam(':changeID', $this->changeID);
						$sqlUpdate->bindParam(':changeNumber', $this->changeNumber);
						$sqlUpdate->bindParam(':changeQuickbooks', $this->changeQuickbooks);
						$sqlUpdate->bindParam(':evaluationID', $this->evaluationID);

						if ($sqlUpdate->execute()) {
							$this->results = 'true';
						}
					}
				}

			} 
		}

		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>