<?php

	
	class ProjectStatus {
		
		private $db;
		private $projectID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setProject($projectID) {
			$this->projectID = $projectID;
		}
			
			
		public function getProject() {
			
			if (!empty($this->projectID)) {

				
				$st = $this->db->prepare("SELECT e.projectID, e.evaluationID, e.evaluationFinalized, e.finalReportSent, 
					CASE WHEN b.bidFirstSent IS NOT NULL THEN b.bidFirstSent ELSE customBid.bidFirstSent END AS bidFirstSent, 
					CASE WHEN b.bidAccepted IS NOT NULL THEN b.bidAccepted ELSE customBid.bidAccepted END AS bidAccepted

				FROM evaluation AS e
				LEFT JOIN evaluationBid AS b ON e.evaluationID = b.evaluationID
				LEFT JOIN customBid ON customBid.evaluationID = e.evaluationID

				WHERE e.projectID=? AND e.deletedAt IS NULL");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->projectID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$allEvals[] = $row;
						
						$this->results = $allEvals; 
					
					}
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>