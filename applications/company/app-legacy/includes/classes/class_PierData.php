<?php


class PierData
{

    private $db;
    private $evaluationID;
    private $getFromPieringCount;
    private $results;


    public function __construct()
    {

        $this->db = new Connection();
        $this->db = $this->db->dbConnect();

    }

    public function setProject($evaluationID)
    {
        $this->evaluationID = $evaluationID;
    }

    public function setGetFromPieringCount($getFromPieringCount)
    {
        $this->getFromPieringCount = $getFromPieringCount;
    }

    public function getPierData()
    {
        if (!empty($this->evaluationID)) {
            if ($this->getFromPieringCount == 1) {
                $st = $this->db->prepare("SELECT 
				evaluationID, sortOrder, pierType, quantity, companyID, pricingPierID, 
				name AS pierTypeName, 
				description AS pierTypeDescription

				FROM 
				
				evaluationPieringCount
				
				LEFT JOIN pricingPier ON pricingPier.pricingPierID = evaluationPieringCount.pierType
				
				WHERE evaluationPieringCount.evaluationID = ? ORDER BY evaluationPieringCount.sortOrder ASC");
            } else {
                $st = $this->db->prepare("SELECT 
                    evaluationID, pierSortOrder, pierNumber, pierSpacing, pierType, 
                    structureStories, structureMaterial, foundationMaterial, 
                    foundationDepth, veneer, veneerStories, companyID, pricingPierID, 
                    name AS pierTypeName, 
                    description AS pierTypeDescription
    
                    FROM 
                    
                    evaluationPieringData AS d
                    
                    LEFT JOIN pricingPier AS p ON p.pricingPierID = d.pierType
                    
                    WHERE d.evaluationID = ? ORDER BY d.pierSortOrder ASC");
            }
            //write parameter query to avoid sql injections
            $st->bindParam(1, $this->evaluationID);
            $st->execute();

            if ($st->rowCount() >= 1) {
                while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                    $returnPierData[] = $row;
                }

                $this->results = $returnPierData;
            }

        }
    }

    public function getResults()
    {
        return $this->results;
    }
}

