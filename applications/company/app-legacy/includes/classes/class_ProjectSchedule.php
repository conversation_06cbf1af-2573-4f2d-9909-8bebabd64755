<?php


use Common\Models\GoogleCalendarEvent;
use Core\Components\DB\StaticAccessors\DB;

class ProjectSchedule {
		
		private $db;
		private $projectID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProject($projectID, $companyID) {
			$this->projectID = $projectID;
			$this->companyID = $companyID;
		}
			
			
		public function getSchedule() {
			
			if (!empty($this->projectID)) {
				
				$st = $this->db->prepare("SELECT 
				
				s.projectScheduleID, g.googleID, s.scheduledUserID, s.scheduleType, s.description, s.scheduledStart, s.scheduledEnd, 
				s.createdByUserID as scheduledByUserID, s.createdAt as scheduledOn, s.cancelledByUserID, s.cancelledAt as cancelledDate, 
				DATE(s.completedAt) as installationComplete, s.completedAt as installationCompleteRecordedDT, 
				completedByUserID as installationCompleteRecordedByUserID, i.userFirstName AS completedFirstName, 
				i.userLastName AS completedLastName,  u.userFirstName AS scheduledFirstName, u.userLastName AS scheduledLastName, 
				d.userFirstName AS scheduledByFirstName, d.userLastName AS scheduledByLastName, 
				c.userFirstName AS cancelledFirstName, c.userLastName AS cancelledLastName, isPending, isCalendarPushInProgress, 
				isSyncInProgress, sendNotifications, replacedAt, r.userFirstName AS replacedByFirstName, r.userLastName AS replacedByLastName, pendingAt

				FROM
				
				projectSchedule AS s
				
           	JOIN project AS j ON j.projectID = s.projectID
				JOIN property AS p ON p.propertyID = j.propertyID
           	JOIN customer AS t ON t.customerID = p.customerID
           	    LEFT JOIN googleCalendarEvents AS g ON g.itemID = s.projectScheduleID AND g.itemType = :eventType
				LEFT JOIN user AS u ON u.userID = s.scheduledUserID 
				LEFT JOIN user AS d ON d.userID = s.createdByUserID 
				LEFT JOIN user AS c ON c.userID = s.cancelledByUserID 
				LEFT JOIN user AS i ON i.userID = s.completedByUserID
				LEFT JOIN user AS r ON r.userID = s.replacedByUserID
									
				WHERE s.deletedAt IS NULL AND s.projectID = :projectID AND t.companyID = :companyID ORDER BY s.scheduleType ASC, s.scheduledStart ASC");
				//write parameter query to avoid sql injections
				$st->bindParam('projectID', $this->projectID);
				$st->bindValue('eventType', GoogleCalendarEvent::TYPE_PROJECT);
				$st->bindParam('companyID', $this->companyID);
				
				$st->execute();
				
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnProject[] = $row;
						
						$this->results = $returnProject; 
						
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>