<?php

	
	class Drawing {
		
		private $db;
		private $evaluationID;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setDrawing($evaluationID) {
			$this->evaluationID = $evaluationID;
			
		}
		
		
			
		public function getDrawing() {
			
			if (!empty($this->evaluationID)) {
				
				$st = $this->db->prepare("SELECT evaluationDrawing.*
                FROM evaluationDrawing 
                JOIN evaluation ON evaluation.evaluationID = evaluationDrawing.evaluationID AND evaluation.deletedAt IS NULL
                WHERE evaluationDrawing.evaluationID=? ORDER BY evaluationDrawing.evaluationDrawingDate DESC LIMIT 1 
				");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->evaluationID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnPhotos = $row;
					}
					
					$this->results = $returnPhotos;
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>