<?php

	
	class Resources {
		
		private $db;
		private $companyID;
		private $filter;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setCompany($companyID, $filter) {
			$this->companyID = $companyID;

            if (!empty($filter)){
                $this->filter = implode(",",$filter);

                $this->userFilter = 'AND userID IN ('.$this->filter.')';
            } else {
                $this->userFilter = 'AND (installation = "1" OR  sales = "1")';
            }
		}

			
		public function getResources() {
			
			if (!empty($this->companyID)) {

                $st = $this->db->prepare("SELECT userID, userFirstName, userLastName, installation, sales, calendarBgColor, calendarTextColor FROM user 
                      WHERE userActive = '1' AND companyID = :companyID ".$this->userFilter." ORDER BY userID ASC");

				//write parameter query to avoid sql injections
				$st->bindParam(':companyID', $this->companyID);
				
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$allUsers[] = $row;
						
					$this->results = $allUsers; 
					
					}
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>