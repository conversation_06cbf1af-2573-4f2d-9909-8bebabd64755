<?php


use Core\Components\DB\StaticAccessors\DB;

class ProjectEmail
	 {
		
		private $db;
		private $projectID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProjectID($projectID) {
			$this->projectID = $projectID;
		}
			
		public function getProjectEmails() {
			
			if (!empty($this->projectID)) {
				
				$st = $this->db->prepare("SELECT projectEmailID, projectID, name, phoneNumber, email, createdAt, createdByUserID, updatedAt, updatedByUserID FROM 
	
				projectEmail WHERE projectID = :projectID AND deletedAt IS NULL");
				
				//write parameter query to avoid sql injections
				$st->bindParam("projectID", $this->projectID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnProjectEmails[] = $row;
						
						$this->results = $returnProjectEmails; 
					}
				} 
				else{
					$this->results = NULL;
				}
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>