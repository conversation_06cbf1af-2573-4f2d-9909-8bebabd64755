<?php


  
class GetCrewman {
    
    private $db;
    private $crewmanID;
    private $companyID;
    private $results;

    public function __construct() {
      
      $this->db = new Connection();
      $this->db = $this->db->dbConnect();
      
      }

    public function setCrewmanID($crewmanID){
      $this->crewmanID = $crewmanID;
    }

    public function setCompanyID($companyID){
      $this->companyID = $companyID;
    }

    public function getCrewman($hourly) {
      
        if (!empty($this->crewmanID) && !empty($this->companyID)) {

            $getHourly = '';
            if ($hourly === 1) {
                $getHourly = ' `hourlyPayRate`,';
            }

            $sql = 'SELECT `crewmanID`, `userID`, `companyID`, `firstName`, `lastName`, `email`, `address`, `address2`, `city`, `state`, `zip`,'.$getHourly.' `crewmanAdded`,
                      `crewmanAddedByID`, `crewmanEdited`, `crewmanEditedByID`, `currentProjectID`, `installerUserID`, `crewmanActive`
                      FROM crewman as c
                      WHERE c.crewmanID = :crewmanID AND c.companyID = :companyID';

            $st = $this->db->prepare($sql);
            //write parameter query to avoid sql injections
            $st->bindParam(":crewmanID", $this->crewmanID);
            $st->bindParam(":companyID", $this->companyID);

            $st->execute();

            if ($st->rowCount()==1) {
                while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
                    $data = $row;
                }
                $this->results = $data;
            }
            else{
                $this->results =  "crewman doesn't exist";
            }
        }
        else {
            $this->results =  "no crewmanID";
        }
    }

    
    public function getResults () {
      return $this->results;
    }
    
}
  

?>