<?php


use App\Classes\Acl;
use App\Resources\Bid\ItemResource;
use App\Resources\Project\NoteResource;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Components\Resource\Classes\Entity;
use Core\Components\Resource\Classes\Scope;

class Evaluation {
		
		private $db;
		private $projectID;
		private $userID;
		private $evaluationID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setEvaluation($projectID, $userID, $evaluationID) {
			$this->projectID = $projectID;
			$this->userID = $userID;
			$this->evaluationID = $evaluationID;
		}
			
			
		public function sendEvaluation() {
			
			if (!empty($this->projectID) && !empty($this->userID) && !empty($this->evaluationID)) {
                $query = $this->db->prepare('SELECT IF(bidItemID IS NOT NULL, HEX(bidItemID), NULL) as bidItemID FROM evaluation WHERE evaluationID = :evaluationID AND deletedAt IS NULL');
                $query->execute([':evaluationID' => $this->evaluationID]);
                $bidItemID = $query->fetchColumn();
                if ($bidItemID === false) {
                    throw new Exception('Unable to find bid item ID for evaluation');
                }

                if ($bidItemID !== null) {
                    $acl = Auth::acl();
                    if ($acl === null) {
                        $acl = Acl::make();
                    }
                    ItemResource::make($acl)->partialUpdate(Entity::make([
                        'id' => $bidItemID,
                        'status' => ItemResource::STATUS_CANCELLED
                    ]))
                        ->force(true)
                        ->findConfig([
                            'check_mutability' => false
                        ])
                        ->run();
                }

				$st = $this->db->prepare("UPDATE `evaluation`
					SET	
			
					`evaluationCancelled` = UTC_TIMESTAMP,
					`evaluationCancelledByID` = :userID
					
					WHERE evaluationID = :evaluationID AND projectID = :projectID");
				
				$st->bindParam(':userID', $this->userID);	
				$st->bindParam(':evaluationID', $this->evaluationID); 
				$st->bindParam(':projectID', $this->projectID);	 
					 
				
				if ($st->execute()) { 
					$this->results = 'true'; 
				}

                $note_resource = NoteResource::make(Auth::acl());
                $note_scope = Scope::make()
                    ->fields(['id'])
                    ->filter('project_id', 'eq', $this->projectID)
                    ->filter('tied_id', 'eq', $this->evaluationID);
                $notes = $note_resource->collection()->scope($note_scope)->run();
                if (count($notes) > 0) {
                    foreach ($notes as $note) {
                        $note_resource->delete($note)->run();
                    }
                }

			}
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>