<?php


use Common\Models\BidItem;

class Evaluation {
		
		private $db;
		private $projectID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setProject($projectID) {
			$this->projectID = $projectID;
		}
			
			
		public function getEvaluation() {
			
			if (!empty($this->projectID)) {
				
				$st = $this->db->prepare("
				(SELECT 1 AS evaluationType, 
				e.evaluationID, 
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				NULL as bidItemID,
				IF(e.bidItemID IS NOT NULL, HEX(e.bidItemID), NULL) as bidFileID,
				e.evaluationCreated, 
				e.evaluationCreatedByID, 
				c.userFirstName AS evalCreatedByFirstName, 
				c.userLastName AS evalCreatedByLastName, 
				e.evaluationLastUpdated, 
				e.evaluationLastUpdatedByID, 
				l.userFirstName AS evalLastUpdatedFirstName, 
				l.userLastName AS evalLastUpdatedLastName, 
				e.evaluationCancelled, 
				e.evaluationCancelledByID, 
				d.userFirstName AS evalCancelledFirstName,
				d.userLastName AS evalCancelledLastName, 
				e.evaluationFinalized, 
				e.evaluationFinalizedByID, 
				f.userFirstName AS evaluationFinalizedFirstName,
				f.userLastName AS evaluationFinalizedLastName, 
				e.finalReportSent,
				e.finalReportSentByID,
				g.userFirstName AS finalReportSentFirstName,
				g.userLastName AS finalReportSentLastName,
				b.isBidCreated, 
				b.bidID,
				b.bidNumber AS referenceID,
				b.bidAcceptanceAmount, 
				b.bidAcceptanceSplit, 
				b.bidAcceptanceDue, 
				b.bidAcceptanceNumber, 
				b.projectStartAmount, 
				b.projectStartSplit, 
				b.projectStartDue, 
				b.projectStartNumber, 
				b.projectCompleteAmount, 
				b.projectCompleteSplit, 
				b.projectCompleteDue, 
				b.projectCompleteNumber, 
				b.bidTotal, 
				b.bidFirstSent, 
				b.bidFirstSentByID, 
				s.userFirstName AS bidFirstSentFirstName, 
				s.userLastName AS bidFirstSentLastName, 
				b.bidLastSent, 
				b.bidLastViewed, 
				b.bidAccepted, 
				b.bidRejected, 
				b.contractID,
				null AS importedDate,
				null AS submittedAt,
				cu.companyID
				
				FROM evaluation AS e
				LEFT JOIN evaluationBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN user c ON c.userID = e.evaluationCreatedByID
				LEFT JOIN user l ON l.userID = e.evaluationLastUpdatedByID
				LEFT JOIN user d ON d.userID = e.evaluationCancelledByID
				LEFT JOIN user s ON s.userID = b.bidFirstSentByID
				LEFT JOIN user f ON f.userID = e.evaluationFinalizedByID
				LEFT JOIN user g ON g.userID = e.finalReportSentByID
                LEFT JOIN project p ON p.projectID = e.projectID
                LEFT JOIN customer cu ON cu.customerID = p.customerID
				
				WHERE e.projectID = :projectID AND customEvaluation IS NULL AND (e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy) AND e.deletedAt IS NULL)
				UNION ALL
				(SELECT 
				
				2 AS evaluationType, e.evaluationID, 
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				HEX(e.bidItemID) as bidItemID,
				HEX(e.bidItemID) as bidFileID,
				e.evaluationCreated, 
				e.evaluationCreatedByID, 
				c.userFirstName AS evalCreatedByFirstName, 
				c.userLastName AS evalCreatedByLastName, 
				e.evaluationLastUpdated, 
				e.evaluationLastUpdatedByID, 
				l.userFirstName AS evalLastUpdatedFirstName, 
				l.userLastName AS evalLastUpdatedLastName, 
				e.evaluationCancelled, 
				e.evaluationCancelledByID, 
				d.userFirstName AS evalCancelledFirstName,
				d.userLastName AS evalCancelledLastName,  
				e.evaluationFinalized, 
				e.evaluationFinalizedByID, 
				f.userFirstName AS evaluationFinalizedFirstName,
				f.userLastName AS evaluationFinalizedLastName, 
				e.finalReportSent,
				e.finalReportSentByID,
				g.userFirstName AS finalReportSentFirstName,
				g.userLastName AS finalReportSentLastName,
				b.isBidCreated, 
				b.bidID,
				IF(e.bidItemID IS NOT NULL AND bi.type = 2, bi.referenceID, b.bidNumber) AS referenceID,
				b.bidAcceptanceAmount, 
				b.bidAcceptanceSplit, 
				b.bidAcceptanceDue, 
				b.bidAcceptanceNumber, 
				b.projectStartAmount, 
				b.projectStartSplit, 
				b.projectStartDue, 
				b.projectStartNumber, 
				b.projectCompleteAmount, 
				b.projectCompleteSplit, 
				b.projectCompleteDue, 
				b.projectCompleteNumber, 
				b.bidTotal, 
				b.bidFirstSent, 
				b.bidFirstSentByID, 
				s.userFirstName AS bidFirstSentFirstName, 
				s.userLastName AS bidFirstSentLastName, 
				b.bidLastSent, 
				b.bidLastViewed, 
				b.bidAccepted, 
				b.bidRejected, 
				b.contractID,
				b.importedDate,
				bi.submittedAt,
				cu.companyID
				
				FROM evaluation AS e
				LEFT JOIN customBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN user c ON c.userID = e.evaluationCreatedByID
				LEFT JOIN user l ON l.userID = e.evaluationLastUpdatedByID
				LEFT JOIN user d ON d.userID = e.evaluationCancelledByID
				LEFT JOIN user s ON s.userID = b.bidFirstSentByID
				LEFT JOIN user f ON f.userID = e.evaluationFinalizedByID
				LEFT JOIN user g ON g.userID = e.finalReportSentByID
				LEFT JOIN project p ON p.projectID = e.projectID
                LEFT JOIN customer cu ON cu.customerID = p.customerID
				
				WHERE e.projectID = :projectID AND (customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type = :bidItemTypeGuided)) AND e.deletedAt IS NULL)
				
				ORDER BY evaluationCreated ASC
				");
				//write parameter query to avoid sql injections
                $st->bindValue('bidItemTypeLegacy', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeGuided', BidItem::TYPE_GUIDED);
				$st->bindParam('projectID', $this->projectID);
				
				$st->execute();
				
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnEvaluation[] = $row;
						
						$this->results = $returnEvaluation; 
						
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>