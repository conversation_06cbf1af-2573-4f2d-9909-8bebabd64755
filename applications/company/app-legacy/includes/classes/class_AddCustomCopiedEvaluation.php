<?php

use App\Services\BidItemCopyService;
use Common\Models\BidItem;
use Core\Components\Auth\StaticAccessors\Auth;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Ramsey\Uuid\Uuid;

class Evaluation {
		
		private $db;
		private $companyID;
		private $projectID;
		private $userID;
		private $copiedEvaluationID;
		private $description;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setEvaluation($companyID, $projectID, $userID, $copiedEvaluationID, $description) {
			$this->companyID = $companyID;
			$this->projectID = $projectID;
			$this->userID = $userID;
			$this->copiedEvaluationID = $copiedEvaluationID;
			$this->description = $description;
		}
			
			
		public function sendEvaluation() {
			
			if (!empty($this->projectID) && !empty($this->userID) && !empty($this->copiedEvaluationID) && !empty($this->description)) {
			    $query = $this->db->prepare('
                    SELECT IF(e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy, HEX(e.bidItemID), NULL) as bidItemID
                    FROM evaluation AS e
                    LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
                    WHERE e.evaluationID = :evaluationID');
			    $query->execute([':evaluationID' => $this->copiedEvaluationID, ':bidItemTypeLegacy' => BidItem::TYPE_LEGACY]);
			    $bidItemID = $query->fetchColumn();
			    if ($bidItemID === false) {
			        throw new Exception('Unable to find bid item ID for evaluation');
                }

                if ($bidItemID !== null) {
                    $copy_service = new BidItemCopyService(Auth::acl(), $bidItemID);
                    $copy_service->run();
                    $bidItemID = $copy_service->getId();
                }

				//Evaluation
				$stOne = $this->db->prepare("
				INSERT INTO evaluation(evaluationID, evaluationUUID, projectID, evaluationDescription, customEvaluation, bidItemID, isPiering, isWallRepair, isWaterManagement, isSupportPosts, isCrackRepair, isMudjacking,
				structureType, structureTypeOther, frontFacingDirection, isStructureAttached, StructureAttachedDescription, structureMaterial, generalFoundationMaterial, isWalkOutBasement, evaluationCreated,
				evaluationCreatedByID, evaluationLastUpdated, evaluationLastUpdatedByID, isSendToEngineer, evaluationCancelled, evaluationCancelledByID)
				
				SELECT NULL, :evaluationUUID, :projectID, :description, customEvaluation, :bidItemID, isPiering, isWallRepair, isWaterManagement, isSupportPosts, isCrackRepair, isMudjacking, structureType,
				structureTypeOther, frontFacingDirection, isStructureAttached, StructureAttachedDescription, structureMaterial, generalFoundationMaterial, isWalkOutBasement, UTC_TIMESTAMP,
				:userID, NULL, NULL, NULL, NULL, NULL
				
				FROM evaluation WHERE evaluationID = :copiedEvaluationID AND projectID = :projectID
				");

                $stOne->bindValue(':evaluationUUID', Uuid::uuid4()->getBytes());
				$stOne->bindParam(':projectID', $this->projectID);
				$stOne->bindValue(':bidItemID', $bidItemID !== null ? Uuid::fromString($bidItemID)->getBytes() : null);
				$stOne->bindParam(':description', $this->description);
				$stOne->bindParam(':userID', $this->userID);	
				$stOne->bindParam(':copiedEvaluationID', $this->copiedEvaluationID);	 
				
				$stOne->execute();
					 
				$evaluationID = $this->db->lastInsertId();
				
				
				//Evaluation Bid
				$stTwo = $this->db->prepare("
				INSERT INTO customBid(evaluationID, isBidCreated, bidID, bidAcceptanceAmount, bidAcceptanceSplit, bidAcceptanceDue, bidAcceptanceNumber, projectStartAmount, projectStartSplit, projectStartDue, projectStartNumber, projectCompleteAmount, projectCompleteSplit, projectCompleteDue, projectCompleteNumber, bidTotal, bidFirstSent, bidFirstSentByID, contractID, bidLastSent, bidLastViewed, bidAccepted, bidRejected)
				
				SELECT :evaluationID, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL
				
				FROM customBid WHERE evaluationID = :copiedEvaluationID
				");

				$stTwo->bindParam(':evaluationID', $evaluationID);		
				$stTwo->bindParam(':copiedEvaluationID', $this->copiedEvaluationID);	 
				
				$stTwo->execute();
				
				
				
				
				
				//Copy Documents
                $srcPath = Path::get('evaluationDocument', $this->companyID, $this->projectID, $this->copiedEvaluationID);
                $destPath = Path::get('evaluationDocument', $this->companyID, $this->projectID, $evaluationID);
                if (!is_dir($destPath) && !mkdir($destPath, 0755, true)) {
                    throw new AppException('Unable to create evaluation document directory: %s', $destPath);
                }
				
				if (is_dir($srcPath)) {
					$srcDir = opendir($srcPath);
					while($readFile = readdir($srcDir))
					{
						if($readFile != '.' && $readFile != '..')
						{
							if (!file_exists($destPath . $readFile)) {
								copy($srcPath . $readFile, $destPath . $readFile);
							}
						}
					}
					closedir($srcDir);
	
				}
				
				$this->results = [
				    'evaluation_id' => $evaluationID
                ];
                if ($bidItemID !== null) {
                    $this->results['bid_item_id'] = $bidItemID;
                }
			}
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>