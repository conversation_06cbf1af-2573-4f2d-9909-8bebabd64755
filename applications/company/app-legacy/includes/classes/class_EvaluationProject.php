<?php


use Core\Components\DB\StaticAccessors\DB;

class EvaluationProject
    {
        private $evaluationID;
        private $companyID;
        private $customEvaluation;
        private $results;

        public function setEvaluation($evaluationID, $companyID, $customEvaluation)
        {
            $this->evaluationID = $evaluationID;
            $this->companyID = $companyID;
            $this->customEvaluation = $customEvaluation;
        }

        public function getEvaluation()
        {
            //FXLRATR-177; added b.bidAcceptanceName & b.projectCompleteName
            if (!empty($this->evaluationID)) {
                if (empty($this->customEvaluation)) {
                    $result = DB::selectOne("SELECT 
                        p.projectID,
                        p.referenceID AS projectReferenceID,
                        p.projectSalesperson,
                        p.projectDescription,
                        p.projectCancelled,
                        p.projectCompleted,
                        t.propertyID, 
                        m.customerID, 
                        t.address,
                        t.address2,  
                        t.city, 
                        t.state, 
                        t.zip, 
                        m.quickbooksID,
                        m.firstName, 
                        m.lastName, 
                        m.businessName,
                        m.email, 
                        m.ownerAddress, 
                        m.ownerAddress2, 
                        m.ownerCity, 
                        m.ownerState, 
                        m.ownerZip,
                        m.isUnsubscribed AS unsubscribed,
                        IF(m.email IS NULL, 1, 0) AS noEmailRequired, 
                        c.defaultInvoices,
                        c.invoiceSplitBidAcceptance,
                        c.invoiceSplitProjectComplete,
                        b.bidAcceptanceName, 
                        b.bidAcceptanceAmount, 
                        b.bidAcceptanceDue, 
                        b.bidAcceptanceNumber, 
                        b.projectStartAmount,
                        b.projectStartDue, 
                        b.projectStartNumber,
                        b.projectCompleteName, 
                        b.projectCompleteAmount, 
                        b.projectCompleteDue, 
                        b.projectCompleteNumber, 
                        b.bidScopeChangeTotal,
                        b.bidScopeChangeType,
                        b.bidScopeChangeQuickbooksID,
                        b.bidScopeChangeNumber,
                        b.bidScopeChangeQuickbooks,
                        b.bidScopeChangePaid,
                        b.bidFirstSent, 
                        b.bidAccepted, 
                        b.bidAcceptedName,
                        b.bidRejected, 
                        b.contractID, 
                        b.bidNumber,
                        b.invoicePaidAccept,
                        b.invoicePaidComplete,
                        e.evaluationDescription,
                        e.evaluationCreated, 
                        e.evaluationCancelled, 
                        e.evaluationCreatedByID,
                        e.finalReportSent,
                        u.userFirstName AS createdFirstName, 
                        u.userLastName AS createdLastName, 
                        u.userEmail AS createdEmail, 
                        n.phoneNumber AS createdPhone,
                        u2.userFirstName AS salesFirstName,
                        u2.userLastName AS salesLastName,
                        u2.userEmail AS salesEmail,
                        u2p.phoneNumber AS salesPhone
					FROM evaluation AS e
                        LEFT JOIN evaluationBid AS b ON b.evaluationID = e.evaluationID 
                        JOIN project AS p ON p.projectID = e.projectID 
                        LEFT JOIN user AS u2 ON u2.userID = p.projectSalesperson
					    LEFT JOIN userPhone AS u2p ON u2p.userID = p.projectSalesperson AND u2p.isPrimary = '1'
                        JOIN property AS t ON t.propertyID = p.propertyID
                        JOIN customer AS m ON m.customerID = t.customerID 
                        JOIN companies AS c ON c.companyID = m.companyID 
                        LEFT JOIN user AS u ON u.userID = e.evaluationCreatedByID 
                        LEFT JOIN userPhone AS n ON n.userID = e.evaluationCreatedByID AND n.isPrimary = '1'
					WHERE e.evaluationID = ? 
					    AND e.deletedAt IS NULL
					    AND c.companyID = ? 
                    LIMIT 1", [$this->evaluationID, $this->companyID]);

                    if ($result !== null) {
                        $this->results = (array) $result;
                    }
                }
                else {
                    $result = DB::selectOne("SELECT 
                        p.projectID,
                        p.referenceID AS projectReferenceID,
                        p.projectSalesperson,
                        p.projectDescription,
                        p.projectCancelled,
                        p.projectCompleted,
                        t.propertyID, 
                        m.customerID, 
                        t.address,
                        t.address2,  
                        t.city, 
                        t.state, 
                        t.zip, 
                        m.quickbooksID,
                        m.firstName, 
                        m.lastName,
                        m.businessName,
                        m.email, 
                        m.ownerAddress, 
                        m.ownerAddress2, 
                        m.ownerCity, 
                        m.ownerState, 
                        m.ownerZip,
                        m.isUnsubscribed AS unsubscribed,
                        IF(m.email IS NULL, 1, 0) AS noEmailRequired, 
                        c.invoiceSplitBidAcceptance,
                        c.invoiceSplitProjectComplete,
                        b.bidAcceptanceName, 
                        b.bidAcceptanceAmount, 
                        b.bidAcceptanceDue, 
                        b.bidAcceptanceNumber, 
                        b.projectStartAmount,
                        b.projectStartDue, 
                        b.projectStartNumber,
                        b.projectCompleteName, 
                        b.projectCompleteAmount, 
                        b.projectCompleteDue, 
                        b.projectCompleteNumber, 
                        b.bidScopeChangeTotal,
                        b.bidScopeChangeType,
                        b.bidScopeChangeQuickbooksID,
                        b.bidScopeChangeNumber,
                        b.bidScopeChangeQuickbooks,
                        b.bidScopeChangePaid,
                        b.bidFirstSent, 
                        b.bidAccepted, 
                        b.bidAcceptedName,
                        b.bidRejected, 
                        b.contractID,
                        b.bidNumber,
                        b.invoicePaidAccept,
                        b.invoicePaidComplete,
                        e.evaluationDescription,
                        e.evaluationCreated,  
                        e.evaluationCancelled, 
                        e.evaluationCreatedByID,
                        e.finalReportSent,
                        u.userFirstName AS createdFirstName, 
                        u.userLastName AS createdLastName, 
                        u.userEmail AS createdEmail, 
                        n.phoneNumber AS createdPhone,
                        u2.userFirstName AS salesFirstName,
                        u2.userLastName AS salesLastName,
                        u2.userEmail AS salesEmail,
                        u2p.phoneNumber AS salesPhone
					FROM evaluation AS e
                        LEFT JOIN customBid AS b ON b.evaluationID = e.evaluationID 
                        JOIN project AS p ON p.projectID = e.projectID 
                        LEFT JOIN user AS u2 ON u2.userID = p.projectSalesperson
					    LEFT JOIN userPhone AS u2p ON u2p.userID = p.projectSalesperson AND u2p.isPrimary = '1'
                        JOIN property AS t ON t.propertyID = p.propertyID
                        JOIN customer AS m ON m.customerID = t.customerID 
                        JOIN companies AS c ON c.companyID = m.companyID 
                        LEFT JOIN user AS u ON u.userID = e.evaluationCreatedByID 
                        LEFT JOIN userPhone AS n ON n.userID = e.evaluationCreatedByID AND n.isPrimary = '1'
					WHERE e.evaluationID = ? 
					    AND e.deletedAt IS NULL
					    AND c.companyID = ?
                    LIMIT 1", [$this->evaluationID, $this->companyID]);

                    if ($result !== null) {
                        $this->results = (array) $result;
                    }
                }
            }
        }

        public function getResults()
        {
            return $this->results;
        }
    }
	

?>