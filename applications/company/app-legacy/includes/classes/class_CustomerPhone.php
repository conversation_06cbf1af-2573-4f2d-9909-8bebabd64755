<?php


use Core\Components\DB\StaticAccessors\DB;

class CustomerPhone
	 {
		
		private $db;
		private $customerID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setCustomer($customerID) {
			$this->customerID = $customerID;
		}
			
			
		public function getPhone() {
			
			if (!empty($this->customerID)) {
				$sql =<<<SQL
SELECT customerPhone.customerPhoneID, customerPhone.type, customerPhone.phoneNumber, customerPhone.phoneDescription, customerPhone.isPrimary, customerPhone.createdAt, customerPhone.updatedAt
FROM customerPhone
JOIN customer ON customer.customerID = customerPhone.customerID
WHERE customerPhone.customerID = :customerID AND customer.deletedAt IS NULL AND customerPhone.deletedAt IS NULL
SQL;
				$st = $this->db->prepare($sql);
				
				//write parameter query to avoid sql injections
				$st->bindParam("customerID", $this->customerID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCustomer[] = $row;
						
						$this->results = $returnCustomer; 
						
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>