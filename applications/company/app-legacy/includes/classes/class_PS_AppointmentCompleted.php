<?php

use App\Services\TimeService;
use Carbon\Carbon;
use Core\Components\DB\StaticAccessors\DB;
use Core\StaticAccessors\App;

	class AppointmentCompleted {

		private $db;
		private $companyID;
		private $sort;
		private $results;

		public function __construct() {

			$this->db = DB::getPdo();

			}

		public function setStatus($companyID, $sort, $groupBy) {
			$this->companyID = $companyID;
			$this->sort = $sort;
            $this->groupBy = $groupBy;

			/** @var TimeService $time_service */
            $time_service = App::get(TimeService::class);
			$this->time = $time_service->get(Carbon::now('UTC'))->format('Y-m-d H:i:s');
		}

		public function setUser($userID) {
			$this->userID = $userID;
		}

		public function getStatus() {

			if (!empty($this->companyID)) {

				$sqlStatement = "SELECT 'AppointmentCompleted' AS statusType, c.firstName, c.lastName, c.businessName, 
                scheduledEnd AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,
                
                IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson', user.userActive

                FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN (SELECT s1.projectID, s1.scheduledStart, s1.scheduledEnd 
					FROM projectSchedule s1 
					LEFT JOIN projectSchedule s2
						ON (s1.projectID = s2.projectID AND s1.scheduledStart < s2.scheduledStart
							AND s2.cancelledAt IS NULL AND s2.replacedAt IS NULL AND s2.deletedAt IS NULL AND s2.scheduleType = 'Evaluation' AND s2.scheduledStart < :time1) 
						WHERE s2.scheduledStart IS NULL AND s1.cancelledAt IS NULL AND s1.replacedAt IS NULL AND s1.deletedAt IS NULL AND s1.scheduleType = 'Evaluation' AND s1.scheduledStart < :time2
				) AS appt_result_aggregate ON appt_result_aggregate.projectID = j.projectID

				LEFT JOIN (SELECT COUNT(*) AS bidCount, projectID
          			FROM evaluation
             				WHERE evaluationCancelled IS NULL
             				AND deletedAt IS NULL
            			GROUP BY projectID
         			) AS bids_result_aggregate ON bids_result_aggregate.projectID = j.projectID
					
                LEFT JOIN user ON user.userID = j.projectSalesperson
				
				WHERE 
				
				c.deletedAt IS NULL AND
				bidCount IS NULL AND
				
				c.companyID = :companyID ORDER BY";

                if ($this->groupBy) {
                    $sqlStatement = $sqlStatement . ' user.userLastName ASC, user.userFirstName ASC,';
                }

                $sqlStatement = $sqlStatement . ' scheduledEnd';

                if ($this->sort == 'asc') {
                    $sqlStatement = $sqlStatement . ' ASC';

                } else {
                    $sqlStatement = $sqlStatement . ' DESC';
                }

				$st = $this->db->prepare($sqlStatement);

				$st->bindParam(':companyID', $this->companyID);
				$st->bindParam(':time1', $this->time);
                $st->bindParam(':time2', $this->time);

				$st->execute();

				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$notificationArray[] = $row;

						$this->results = $notificationArray;
					}

				}

			}
		}

		public function getStatusUser() {

			if (!empty($this->companyID) && !empty($this->userID)) {

				$sqlStatement = "SELECT 'AppointmentCompleted' AS statusType, c.firstName, c.lastName, c.businessName, 
                scheduledEnd AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,

                :userID1 AS 'salesID'
                
                FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.projectCompleted IS NULL
					AND j.deletedAt IS NULL

				JOIN (SELECT s1.projectID, s1.scheduledStart, s1.scheduledEnd, s1.scheduledUserID 
					FROM projectSchedule s1 
					LEFT JOIN projectSchedule s2
						ON (s1.projectID = s2.projectID AND s1.scheduledStart < s2.scheduledStart
							AND s2.cancelledAt IS NULL AND s2.replacedAt IS NULL AND s2.deletedAt IS NULL AND s2.scheduleType = 'Evaluation' AND s2.scheduledStart < :time1) 
						WHERE s2.scheduledStart IS NULL AND s1.cancelledAt IS NULL AND s1.replacedAt IS NULL AND s1.deletedAt IS NULL AND s1.scheduleType = 'Evaluation' AND s1.scheduledStart < :time2
				) AS appt_result_aggregate ON appt_result_aggregate.projectID = j.projectID

				LEFT JOIN (SELECT COUNT(*) AS bidCount, projectID
          			FROM evaluation
             				WHERE evaluationCancelled IS NULL
             				AND deletedAt IS NULL
            			GROUP BY projectID
         			) AS bids_result_aggregate ON bids_result_aggregate.projectID = j.projectID
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				bidCount IS NULL AND

				(j.projectSalesperson = :userID2 OR scheduledUserID = :userID3) AND 
				
				c.companyID = :companyID ORDER BY scheduledEnd";

				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';

				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}

				$st = $this->db->prepare($sqlStatement);

				$st->bindParam(':userID1', $this->userID);
                $st->bindParam(':userID2', $this->userID);
                $st->bindParam(':userID3', $this->userID);
				$st->bindParam(':companyID', $this->companyID);
				$st->bindParam(':time1', $this->time);
                $st->bindParam(':time2', $this->time);

				$st->execute();

				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$projectStatusArray[] = $row;

						$this->results = $projectStatusArray;
					}

				}

			}
		}

		public function getResults () {
		 	return $this->results;
		}


	}


?>