<?php

	
	class LastInvoiceNumber {
		
		private $db;
		private $results;
		private $bidID;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}

		public function setCompany($bidID){
			$this->bidID = $bidID;
		}
			
		public function getCompany() {

			$st = $this->db->prepare("SELECT c.companyID FROM 

				evaluationBid AS b
				LEFT JOIN evaluation AS e ON e.evaluationID = b.evaluationID
				LEFT JOIN project AS p ON p.projectID = e.projectID
				LEFT JOIN customer AS c ON c.customerID = p.customerID

				WHERE b.bidID = :bidID");

			$st->bindParam(':bidID', $this->bidID);		
			$st->execute();
			
			if ($st->rowCount()>=1) {
				while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
					$returnCompany = $row['companyID'];

				}
				$companyID = $returnCompany;

			} else {
				$st = $this->db->prepare("SELECT c.companyID FROM 

				customBid AS b
				LEFT JOIN evaluation AS e ON e.evaluationID = b.evaluationID
				LEFT JOIN project AS p ON p.projectID = e.projectID
				LEFT JOIN customer AS c ON c.customerID = p.customerID

				WHERE b.bidID = :bidID");

				$st->bindParam(':bidID', $this->bidID);		
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCompany = $row['companyID'];

					}
					$companyID = $returnCompany;
				} 
			}
			
			if ($companyID != '') {

				$st = $this->db->prepare("SELECT invoiceNumber FROM (

					(SELECT  bidAcceptanceNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `evaluationBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidAcceptanceNumber IS NOT NULL)

					UNION ALL

					(SELECT  projectCompleteNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `evaluationBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE projectCompleteNumber IS NOT NULL)


					UNION ALL

					(SELECT  bidScopeChangeNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `evaluationBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidScopeChangeNumber IS NOT NULL)

					UNION ALL

					(SELECT  bidAcceptanceNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `customBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidAcceptanceNumber IS NOT NULL)

					UNION ALL

					(SELECT  projectCompleteNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `customBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE projectCompleteNumber IS NOT NULL)

					UNION ALL

					(SELECT  bidScopeChangeNumber AS 'invoiceNumber', companyID, b.evaluationID FROM `customBid` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE bidScopeChangeNumber IS NOT NULL)

					UNION ALL

					(SELECT invoiceNumber, companyID, b.evaluationID FROM `evaluationInvoice` as b
					JOIN `evaluation` as e ON e.evaluationID = b.evaluationID 
					JOIN `project` as p ON p.projectID = e.projectID 
					JOIN `property` as t ON t.propertyID = p.propertyID 
					JOIN `customer` as m ON m.customerID = t.customerID WHERE invoiceNumber IS NOT NULL)

				) as t WHERE invoiceNumber != '' AND companyID = :companyID ORDER BY evaluationID DESC, invoiceNumber DESC LIMIT 1");
				$st->bindParam(':companyID', $companyID);		
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnNumber[] = $row;
					}
					
					$this->results = $returnNumber;
				} 
			}
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>