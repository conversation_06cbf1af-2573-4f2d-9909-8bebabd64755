<?php


use Common\Models\BidItem;

class EvaluationInvoices {
		
		private $db;
		private $projectID;
		private $evaluationID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setProject($projectID, $companyID) {
			$this->projectID = $projectID;
			$this->companyID = $companyID;
		}
			

		//1: Bid Acceptance - evaluationBid
		//2: Bid Acceptance - customBid
		//3: Evaluation Invoice - evaluationBid
		//3: Evaluation Invoice - customBid
		//4: Project Complete - evaluationBid
		//5: Project Complete - customBid 	
		//6: Credit Memo/Invoice - evaluationBid
		//7: Credit Memo/Invoice - customBid
			
		public function getProject() {
			
			if (!empty($this->projectID) && !empty($this->companyID)) {
				
				$st = $this->db->prepare("
				SELECT * FROM((SELECT 1 AS invoiceType, 
				e.evaluationID, 
				0 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				NULL as bidItemID,
				b.bidID,
				b.bidAcceptanceName,
				b.bidAcceptanceAmount, 
				b.bidAcceptanceSplit, 
				b.bidAcceptanceNumber,
				b.bidAcceptanceQuickbooksID,
				b.bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN evaluationBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND customEvaluation IS NULL AND (e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy1) AND b.bidTotal IS NOT NULL AND (b.bidAcceptanceSplit != '0.00' OR b.bidAcceptanceAmount > 0.00) AND e.evaluationCancelled IS NULL)

				UNION ALL
					   
				(SELECT 2 AS invoiceType, 
				e.evaluationID, 
				0 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				HEX(e.bidItemID) as bidItemID,
				b.bidID,
				b.bidAcceptanceName, 
				b.bidAcceptanceAmount, 
				b.bidAcceptanceSplit, 
				b.bidAcceptanceNumber,
				b.bidAcceptanceQuickbooksID,
				b.bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN customBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND (customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy2)) AND b.bidTotal IS NOT NULL AND (b.bidAcceptanceSplit != '0.00' OR b.bidAcceptanceAmount > 0.00) AND e.evaluationCancelled IS NULL)
		
				UNION ALL

				(SELECT 3 AS invoiceType, 
				e.evaluationID,
				i.invoiceSort, 
				i.invoiceName, 
				i.invoiceSplit, 
				i.invoiceAmount, 
				i.invoicePaid, 
				i.invoiceNumber,
				i.invoiceQuickbooksID,
				i.isQuickbooks,	
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				NULL as bidItemID,
				NULL AS bidID,
				NULL AS bidAcceptanceName, 
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				NULL AS contractID,
				NULL AS bidAccepted,
				NULL AS invoicePaidAccept,
				NULL AS invoicePaidComplete

				FROM evaluationInvoice AS i

				JOIN evaluation AS e ON e.evaluationID = i.evaluationID AND e.deletedAt IS NULL
				LEFT JOIN evaluationBid AS b ON b.evaluationID = e.evaluationID
				LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND customEvaluation IS NULL AND (e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy3) AND e.evaluationCancelled IS NULL)

				UNION ALL

				(SELECT 3 AS invoiceType, 
				e.evaluationID,
				i.invoiceSort, 
				i.invoiceName, 
				i.invoiceSplit, 
				i.invoiceAmount, 
				i.invoicePaid, 
				i.invoiceNumber,
				i.invoiceQuickbooksID,
				i.isQuickbooks,	
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				HEX(e.bidItemID) as bidItemID,
				NULL AS bidID,
				NULL AS bidAcceptanceName, 
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				NULL AS contractID,
				NULL AS bidAccepted,
				NULL AS invoicePaidAccept,
				NULL AS invoicePaidComplete

				FROM evaluationInvoice AS i

				JOIN evaluation AS e ON e.evaluationID = i.evaluationID AND e.deletedAt IS NULL
				LEFT JOIN customBid AS b ON b.evaluationID = e.evaluationID
				LEFT JOIN bidItems AS bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND (customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy4)) AND e.evaluationCancelled IS NULL)

				UNION ALL

				(SELECT 4 AS invoiceType, 
				e.evaluationID, 
				99 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				NULL as bidItemID,
				b.bidID,
				NULL AS bidAcceptanceName,
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				b.projectCompleteName, 
				b.projectCompleteAmount, 
				b.projectCompleteSplit, 
				b.projectCompleteNumber,
				b.projectCompleteQuickbooksID,
				b.projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN evaluationBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND customEvaluation IS NULL AND (e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy5) AND b.bidTotal IS NOT NULL AND (b.projectCompleteSplit != '0.00' OR b.projectCompleteAmount > 0.00) AND e.evaluationCancelled IS NULL)

				UNION ALL

				(SELECT 5 AS invoiceType, 
				e.evaluationID, 
				99 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				HEX(e.bidItemID) as bidItemID,
				b.bidID,
				NULL AS bidAcceptanceName, 
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				b.projectCompleteName, 
				b.projectCompleteAmount, 
				b.projectCompleteSplit, 
				b.projectCompleteNumber,
				b.projectCompleteQuickbooksID,
				b.projectCompleteQuickbooks,
				NULL AS bidScopeChangeTotal,
				NULL AS bidScopeChangeType,
				NULL AS bidScopeChangeNumber,
				NULL AS bidScopeChangeQuickbooksID,
				NULL AS bidScopeChangeQuickbooks, 
				NULL AS bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN customBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND (customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy6)) AND b.bidTotal IS NOT NULL AND (b.projectCompleteSplit != '0.00' OR b.projectCompleteAmount > 0.00) AND e.evaluationCancelled IS NULL)

				UNION ALL

				(SELECT 6 AS invoiceType, 
				e.evaluationID, 
				9999 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation, 
				NULL as bidItemID,
				b.bidID,
				NULL AS bidAcceptanceName, 
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				b.bidScopeChangeTotal,
				b.bidScopeChangeType,
				b.bidScopeChangeNumber,
				b.bidScopeChangeQuickbooksID,
				b.bidScopeChangeQuickbooks, 
				b.bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN evaluationBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND customEvaluation IS NULL AND (e.bidItemID IS NULL OR bi.type = :bidItemTypeLegacy7) AND b.bidTotal IS NOT NULL AND (b.bidScopeChangeTotal IS NOT NULL OR b.bidScopeChangeQuickbooksID IS NOT NULL) AND e.evaluationCancelled IS NULL)


				UNION ALL

				(SELECT 7 AS invoiceType, 
				e.evaluationID, 
				9999 AS invoiceSort, 
				NULL AS invoiceName, 
				NULL AS invoiceSplit, 
				NULL AS invoiceAmount, 
				NULL AS invoicePaid,
				NULL AS invoiceNumber,
				NULL AS invoiceQuickbooksID,
				NULL AS isQuickbooks,
				e.projectID, 
				e.evaluationDescription, 
				e.customEvaluation,
				HEX(e.bidItemID) as bidItemID,
				b.bidID,
				NULL AS bidAcceptanceName, 
				NULL AS bidAcceptanceAmount, 
				NULL AS bidAcceptanceSplit, 
				NULL AS bidAcceptanceNumber,
				NULL AS bidAcceptanceQuickbooksID,
				NULL AS bidAcceptanceQuickbooks,
				NULL AS projectCompleteName, 
				NULL AS projectCompleteAmount, 
				NULL AS projectCompleteSplit, 
				NULL AS projectCompleteNumber,
				NULL AS projectCompleteQuickbooksID,
				NULL AS projectCompleteQuickbooks,
				b.bidScopeChangeTotal,
				b.bidScopeChangeType,
				b.bidScopeChangeNumber,
				b.bidScopeChangeQuickbooksID,
				b.bidScopeChangeQuickbooks, 
				b.bidScopeChangePaid,
				b.bidTotal,
				b.bidFirstSent,
				b.contractID,
				b.bidAccepted,
				b.invoicePaidAccept,
				b.invoicePaidComplete

				FROM evaluation AS e

				LEFT JOIN customBid b ON e.evaluationID = b.evaluationID
				LEFT JOIN bidItems bi ON bi.bidItemID = e.bidItemID
				LEFT JOIN project p ON p.projectID = e.projectID
				LEFT JOIN property t ON t.propertyID = p.propertyID
				LEFT JOIN customer c ON c.customerID = t.customerID
				
				WHERE e.projectID = :projectID AND e.deletedAt IS NULL AND b.bidAccepted IS NOT NULL AND c.companyID = :companyID AND (customEvaluation IS NOT NULL OR (e.bidItemID IS NOT NULL AND bi.type != :bidItemTypeLegacy8)) AND b.bidTotal IS NOT NULL AND (b.bidScopeChangeTotal IS NOT NULL OR b.bidScopeChangeQuickbooksID IS NOT NULL) AND e.evaluationCancelled IS NULL)


				) as t ORDER BY evaluationID ASC, invoiceSort ASC
			
				");
				
				
				//write parameter query to avoid sql injections
				$st->bindParam('projectID', $this->projectID);
				$st->bindParam('companyID', $this->companyID);
				$st->bindValue('bidItemTypeLegacy1', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy2', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy3', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy4', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy5', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy6', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy7', BidItem::TYPE_LEGACY);
                $st->bindValue('bidItemTypeLegacy8', BidItem::TYPE_LEGACY);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnInvoice[] = $row;
						
					}
					
					$this->results = $returnInvoice; 
				} 
				
			} 
		}

		public function setEvaluation($evaluationID, $companyID) {
			$this->evaluationID = $evaluationID;
			$this->companyID = $companyID;
		}
			
			
		public function getEvaluation() {
			
			if (!empty($this->evaluationID) && !empty($this->companyID)) {
				
				$st = $this->db->prepare("
				SELECT e.evaluationID, i.invoiceSort, i.invoiceName, i.invoiceSplit, i.invoiceAmount, i.invoicePaid FROM 
				`evaluationInvoice` AS i 
				JOIN evaluation AS e ON e.evaluationID = i.evaluationID AND e.deletedAt IS NULL
				JOIN project p ON p.projectID = e.projectID
				JOIN property t ON t.propertyID = p.propertyID
				JOIN customer c ON c.customerID = t.customerID
				WHERE e.evaluationID = :evaluationID AND c.companyID = :companyID ORDER BY invoiceSort ASC
			
				");
				
				
				//write parameter query to avoid sql injections
				$st->bindParam('evaluationID', $this->evaluationID);
				$st->bindParam('companyID', $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnInvoice[] = $row;
						
					}
					
					$this->results = $returnInvoice; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>