<?php


use Core\Components\DB\StaticAccessors\DB;

class Project {
		
		private $db;
		private $projectID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProject($projectID, $companyID) {
			$this->projectID = $projectID;
			$this->companyID = $companyID;
		}
			
			
		public function getProject() {
			
			if (!empty($this->projectID) && !empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT 
					m.customerID, 
					p.projectID, 
					m.firstName, 
					m.lastName, 
					m.quickbooksID,
					t.propertyID, 
					t.address, 
					t.address2, 
					t.city, 
					t.state, 
					t.zip, 
					t.county,
					t.township,
					t.latitude, 
					t.longitude, 
					m.ownerAddress, 
					m.ownerAddress2, 
					m.ownerCity, 
					m.ownerState, 
					m.ownerZip, 
					m.email,
					m.businessName,
					m.isUnsubscribed AS unsubscribed, 
					IF(m.email IS NULL, 1, 0) AS noEmailRequired, 
                    p.referenceID,
					p.projectDescription, 
					p.project<PERSON>alesperson, 
					p.createdAt AS projectAdded, 
					p.projectCancelled, 
					p.projectCancelledByID, 
					p.projectCompleted, 
					p.projectCompletedByID, 
					p.referralMarketingTypeID,
                    p.createdByUserID,
					c.companyID, 
					c.name, 
					c.address as 'companyAddress1', 
					c.address2 as 'companyAddress2', 
					c.city as 'companyCity', 
					c.state as 'companyState', 
					c.zip as 'companyZip', 
					c.website,
					c.color, 
					c.emailReply, 
					c.emailFrom, 
					u1.userFirstName AS cancelledFirstName, 
					u1.userLastName AS cancelledLastName, 
					u1.userEmail AS cancelledEmail, 
					u2.userFirstName AS completedFirstName, 
					u2.userLastName AS completedLastName, 
					u2.userEmail AS completedEmail, 
					u3.userFirstName AS salespersonFirstName, 
					u3.userLastName AS salespersonLastName, 
					u3.userEmail AS salespersonEmail, 
					u3.userActive AS salespersonActive
				
				FROM project AS p 
				
             	LEFT JOIN property AS t ON t.propertyID = p.propertyID
            	LEFT JOIN customer AS m ON m.customerID = t.customerID
            	LEFT JOIN companies AS c ON c.companyID = m.companyID 
				LEFT JOIN user as u1 on u1.userID = p.projectCancelledByID
				LEFT JOIN user as u2 on u2.userID = p.projectCompletedByID
				LEFT JOIN user as u3 on u3.userID = p.projectSalesperson
	
				WHERE p.projectID=? AND p.deletedAt IS NULL AND c.companyID=? LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->projectID);
				$st->bindParam(2, $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnProject = $row;
						
					}
					
					$this->results = $returnProject; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>