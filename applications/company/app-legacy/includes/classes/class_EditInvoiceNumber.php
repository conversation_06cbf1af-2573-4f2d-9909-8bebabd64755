<?php

	
	class Invoice {
		
		private $db;
		private $evaluationID;
		private $customEvaluation;
		private $invoiceName;
		private $invoiceType;
		private $invoiceNumber;
		private $invoiceID;
		private $isQuickbooks;
		private $response;

		
		public function __construct() {
		
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			

		public function setEvaluation($evaluationID, $customEvaluation, $invoiceName, $invoiceType, $invoiceNumber, $invoiceID, $isQuickbooks) {
			$this->evaluationID = $evaluationID;
			$this->customEvaluation = $customEvaluation;
			$this->invoiceName = $invoiceName;
			$this->invoiceType = $invoiceType;
			$this->invoiceNumber = $invoiceNumber;
			$this->invoiceID = $invoiceID;
			$this->isQuickbooks = $isQuickbooks;
		}
			
			
		public function setInvoice() {
			
			if (!empty($this->evaluationID) && $this->customEvaluation == '') {

				if ($this->invoiceType == 'bidAcceptanceNumber') {

					$st = $this->db->prepare("
					UPDATE `evaluationBid` SET 

					bidAcceptanceNumber = :invoiceNumber,
					bidAcceptanceQuickbooksID = :invoiceID,
					bidAcceptanceQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}

				} else if ($this->invoiceType == 'projectCompleteNumber') {

					$st = $this->db->prepare("
					UPDATE `evaluationBid` SET 

					projectCompleteNumber = :invoiceNumber,
					projectCompleteQuickbooksID = :invoiceID,
					projectCompleteQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}

				} else {
					$st = $this->db->prepare("
					UPDATE `evaluationInvoice` SET 

					invoiceNumber = :invoiceNumber,
					invoiceQuickbooksID = :invoiceID,
					isQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID AND invoiceName = :invoiceName");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					$st->bindParam(':invoiceName', $this->invoiceName);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}
				}
				
			} else {
				if ($this->invoiceType == 'bidAcceptanceNumber') {

					$st = $this->db->prepare("
					UPDATE `customBid` SET 

					bidAcceptanceNumber = :invoiceNumber,
					bidAcceptanceQuickbooksID = :invoiceID,
					bidAcceptanceQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}

				} else if ($this->invoiceType == 'projectCompleteNumber') {

					$st = $this->db->prepare("
					UPDATE `customBid` SET 

					projectCompleteNumber = :invoiceNumber,
					projectCompleteQuickbooksID = :invoiceID,
					projectCompleteQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}

				} else {
					$st = $this->db->prepare("
					UPDATE `evaluationInvoice` SET 

					invoiceNumber = :invoiceNumber,
					invoiceQuickbooksID = :invoiceID,
					isQuickbooks = :isQuickbooks

					WHERE evaluationID = :evaluationID AND invoiceName = :invoiceName");
					//write parameter query to avoid sql injections
					$st->bindParam(':invoiceNumber', $this->invoiceNumber);
					$st->bindParam(':invoiceID', $this->invoiceID);
					$st->bindParam(':isQuickbooks', $this->isQuickbooks);
					$st->bindParam(':evaluationID', $this->evaluationID);
					$st->bindParam(':invoiceName', $this->invoiceName);
					
					if ($st->execute()) { 
						$this->results = 'true'; 
					}
				}
			}
		}


		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>