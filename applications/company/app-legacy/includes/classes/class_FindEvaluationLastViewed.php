<?php

	
	class Bid {
		
		private $db;
		private $bidID;
		private $contractorViewing;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setBid($bidID, $contractorViewing) {
			$this->bidID = $bidID;
			$this->contractorViewing = $contractorViewing;
		}
			
			
		public function getEvaluation() {
			
			if (!empty($this->bidID)) {
				
				$st = $this->db->prepare("SELECT b.evaluationID,b.bidNumber, e.customEvaluation, c.companyID, 
                NULL as bidItemID, b.bidLastViewed, e.evaluationFinalizedByID, p.projectSalesperson

                FROM evaluationBid AS b

				JOIN evaluation AS e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
				JOIN project AS p ON  p.projectID = e.projectID
             	JOIN property AS t ON t.propertyID = p.propertyID
            	JOIN customer AS m ON m.customerID = t.customerID
            	JOIN companies AS c ON c.companyID = m.companyID 

				WHERE bidID = :bidID LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam('bidID', $this->bidID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnEvaluation = $row;
					}
					$this->results = $returnEvaluation;
					
					if ($this->contractorViewing != 'true') {
						$secondST = $this->db->prepare("UPDATE `evaluationBid` SET bidLastViewed = UTC_TIMESTAMP WHERE bidID = :bidID");
						//write parameter query to avoid sql injections
						$secondST->bindParam(':bidID', $this->bidID);			
						$secondST->execute();
					}
					
				} else {
					$st = $this->db->prepare("SELECT b.evaluationID, e.customEvaluation, c.companyID, 
                    IF(e.bidItemID IS NULL, NULL, HEX(e.bidItemID)) as bidItemID, b.bidLastViewed, e.evaluationFinalizedByID, p.projectSalesperson
                    
                    FROM customBid AS b

					JOIN evaluation AS e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN project AS p ON  p.projectID = e.projectID
					JOIN property AS t ON t.propertyID = p.propertyID
					JOIN customer AS m ON m.customerID = t.customerID
					JOIN companies AS c ON c.companyID = m.companyID 
	
					WHERE bidID = :bidID LIMIT 1");
					//write parameter query to avoid sql injections
					$st->bindParam('bidID', $this->bidID);
					
					$st->execute();
					
					if ($st->rowCount()==1) {
						while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
							$returnEvaluation = $row;
						}
						$this->results = $returnEvaluation;
						
						if ($this->contractorViewing != 'true') {
							$secondST = $this->db->prepare("UPDATE `customBid` SET bidLastViewed = UTC_TIMESTAMP WHERE bidID = :bidID");
							//write parameter query to avoid sql injections
							$secondST->bindParam(':bidID', $this->bidID);			
							$secondST->execute();
						}
						
					}
					
				}
			} 
		}

		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>