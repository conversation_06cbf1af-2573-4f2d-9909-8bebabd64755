<?php


use Core\Components\DB\StaticAccessors\DB;

class Bid {

		private $bidID;
		private $results;
			
		public function setBid($bidID) {
			$this->bidID = $bidID;
		}
			
			
		public function getEvaluation() {
			
			if (!empty($this->bidID)) {

			    $result = DB::selectOne('SELECT b.evaluationID, b.bidNumber, e.customEvaluation, NULL as bidItemID, IF(bidItemID IS NOT NULL, HEX(bidItemID), NULL) as bidFileID, m.customerID, c.companyID FROM evaluationBid AS b

				JOIN evaluation AS e ON e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
				JOIN project AS p ON  p.projectID = e.projectID
             	JOIN property AS t ON t.propertyID = p.propertyID
            	JOIN customer AS m ON m.customerID = t.customerID
            	JOIN companies AS c ON c.companyID = m.companyID 

				WHERE bidID = ? LIMIT 1', [$this->bidID]);

				if ($result !== null) {
					$this->results = (array) $result;
				} else {
					$result = DB::selectOne('SELECT b.evaluationID, b.bidNumber, e.customEvaluation, IF(e.bidItemID IS NULL, NULL, HEX(e.bidItemID)) as bidItemID, IF(e.bidItemID IS NULL, NULL, HEX(e.bidItemID)) as bidFileID, m.customerID, c.companyID FROM customBid AS b

					JOIN evaluation AS e ON  e.evaluationID = b.evaluationID AND e.deletedAt IS NULL
					JOIN project AS p ON  p.projectID = e.projectID
					JOIN property AS t ON t.propertyID = p.propertyID
					JOIN customer AS m ON m.customerID = t.customerID
					JOIN companies AS c ON c.companyID = m.companyID 
	
					WHERE bidID = ? LIMIT 1', [$this->bidID]);

                    if ($result !== null) {
                        $this->results = (array) $result;
                    }
				}
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
	}
	

?>