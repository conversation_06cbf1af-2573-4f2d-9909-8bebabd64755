<?php


use Core\Components\DB\StaticAccessors\DB;

class Customer
	 {
		
		private $db;
		private $customerID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setCustomer($customerID, $companyID) {
			$this->customerID = $customerID;
			$this->companyID = $companyID;
		}
			
			
		public function getCustomer() {
			
			if (!empty($this->customerID)) {
				$sql =<<<SQL
SELECT 
    customerID, leadID, quickbooksID, businessName, firstName, lastName, email, ownerAddress, ownerAddress2, ownerCity, 
    ownerState, ownerZip, isUnsubscribed AS unsubscribed, unsubscribedAt, IF(email IS NULL, 1, 0) AS noEmailRequired, 
    createdAt, updatedAt
FROM customer
WHERE customerID = :customerID AND companyID = :companyID AND deletedAt IS NULL
LIMIT 1
SQL;
				$st = $this->db->prepare($sql);
				
				//write parameter query to avoid sql injections
				$st->bindParam("customerID", $this->customerID);
				$st->bindParam("companyID", $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCustomer = $row;
						
					}
					
					$this->results = $returnCustomer; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>