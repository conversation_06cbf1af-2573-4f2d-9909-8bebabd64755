<?php

	
	class FinalizeEvaluation {
		
		private $db;
		private $evaluationID;
		private $companyID;
		private $userID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setEvaluation($evaluationID, $companyID, $userID) {
			$this->evaluationID = $evaluationID;
			$this->companyID = $companyID;
			$this->userID = $userID;
		}
			
			
		public function sendEvaluation() {
			
			if (!empty($this->evaluationID) && !empty($this->companyID) && !empty($this->userID)) {
				
				$st = $this->db->prepare("UPDATE evaluation

				LEFT JOIN project ON project.projectID = evaluation.projectID 
				LEFT JOIN property ON property.propertyID = project.propertyID
				LEFT JOIN customer ON customer.customerID = property.customerID 
				
				SET 
				
				evaluation.evaluationFinalized = UTC_TIMESTAMP,
				evaluation.evaluationFinalizedByID = :evaluationFinalizedByID
				
				WHERE evaluation.evaluationID = :evaluationID AND evaluation.deletedAt IS NULL AND customer.companyID = :companyID");
				
				$st->bindParam(':evaluationID', $this->evaluationID);	 
				$st->bindParam(':companyID', $this->companyID);	 
				$st->bindParam(':evaluationFinalizedByID', $this->userID);	 
					 
				
				if ($st->execute()) { 
					$this->results = 'true'; 
				}
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>