<?php

	
	class Company {
		
		private $db;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setCompany($companyID) {
			$this->companyID = $companyID;
		}
			
			
		public function getCompany() {
			
			if (!empty($this->companyID)) {
				
				$st = $this->db->prepare("SELECT 

				companies.companyID, 
				companies.registrationID, 
				companies.name, 
				companies.address, 
				companies.address2, 
				companies.city, 
				companies.state, 
				companies.zip, 
				companies.billingAddress, 
				companies.billingAddress2, 
				companies.billingCity, 
				companies.billingState, 
				companies.billingZip, 
				companies.website,
				companies.logoFileID,
				companies.color, 
				companies.colorHover, 
				companies.emailAddCustomer, 
				companies.emailAddCustomerLastUpdated, 
				companies.emailSchedule, 
				companies.emailScheduleLastUpdated, 
				companies.scheduleEmailSendSales, 
				companies.emailBidSent, 
				companies.emailBidSentLastUpdated, 
				companies.bidEmailSendSales, 
				companies.emailInstallation, 
				companies.emailInstallationLastUpdated, 
				companies.emailBidAccept, 
				companies.emailBidAcceptLastUpdated, 
				companies.bidAcceptEmailSendSales,
				companies.emailBidReject, 
				companies.emailBidRejectLastUpdated,
				companies.bidRejectEmailSendSales, 
				companies.emailFinalPacket, 
				companies.emailFinalPacketLastUpdated, 
				companies.emailInvoice, 
				companies.emailInvoiceLastUpdated, 
				companies.coverLetter, 
				companies.coverLetterLastUpdated, 
				companies.emailFrom, 
				companies.emailReply, 
				companies.defaultInvoices, 
				companies.invoiceSplitBidAcceptance, 
				companies.invoiceSplitProjectComplete, 
				companies.timezone, 
				companies.daylightSavings, 
				companies.recentlyCompletedStatus, 
				companies.customerProfileID, 
				companies.latitude, 
				companies.longitude,
				companies.isActive

				FROM companies

				WHERE companies.companyID=? LIMIT 1");
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnCompany = $row;
						
					}
					
					$this->results = $returnCompany; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>