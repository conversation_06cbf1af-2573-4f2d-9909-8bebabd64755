<?php


use Core\Components\DB\StaticAccessors\DB;

class AppointmentScheduled {
		
		private $db;
		private $companyID;
		private $sort;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setStatus($companyID, $sort, $groupBy) {
			$this->companyID = $companyID;
			$this->sort = $sort;
            $this->groupBy = $groupBy;
		}

		public function setUser($userID) {
			$this->userID = $userID;
		}
			
			
		public function getStatus() {
			
			if (!empty($this->companyID)) {
				
				$sqlStatement = "SELECT 'AppointmentScheduled' AS statusType, c.firstName, c.lastName, c.businessName, 
                s.scheduledStart AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,
                
                IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson', user.userActive
                
                FROM customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.deletedAt IS NULL

				LEFT JOIN projectSchedule AS s ON s.projectID = j.projectID
					AND s.scheduleType = 'Evaluation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL
					
                LEFT JOIN user ON user.userID = j.projectSalesperson
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				s.scheduledStart > NOW() AND 
				
				c.companyID = :companyID ORDER BY";

                if ($this->groupBy) {
                    $sqlStatement = $sqlStatement . ' user.userLastName ASC, user.userFirstName ASC,';
                }

                $sqlStatement = $sqlStatement . ' s.scheduledStart';

                if ($this->sort == 'asc') {
                    $sqlStatement = $sqlStatement . ' ASC';

                } else {
                    $sqlStatement = $sqlStatement . ' DESC';
                }
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':companyID', $this->companyID);	 

				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$projectStatusArray[] = $row;
						
						$this->results = $projectStatusArray;
					}
					
				} 
				
			} 
		}

		public function getStatusUser() {
			
			if (!empty($this->companyID) && !empty($this->userID)) {
				
				$sqlStatement = "SELECT 'AppointmentScheduled' AS statusType, c.firstName, c.lastName, c.businessName, 
                s.scheduledStart AS time, j.projectID AS link, p.address, p.address2, p.city, p.state, p.zip,

                :userID1 AS 'salesID'

                FROM 

				customer AS c
				
				JOIN property AS p ON p.customerID = c.customerID AND p.deletedAt IS NULL
				JOIN project AS j ON j.propertyID = p.propertyID
					AND j.projectCancelled IS NULL
					AND j.deletedAt IS NULL

				LEFT JOIN projectSchedule AS s ON s.projectID = j.projectID
					AND s.scheduleType = 'Evaluation' 
					AND s.cancelledAt IS NULL
					AND s.replacedAt IS NULL
					AND s.deletedAt IS NULL
				
				WHERE 
				
				c.deletedAt IS NULL AND 
				s.scheduledStart > NOW() AND

				(j.projectSalesperson = :userID2 OR s.scheduledUserID = :userID3) AND 
				
				c.companyID = :companyID ORDER BY s.scheduledStart";

				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';
					
				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':userID1', $this->userID);
                $st->bindParam(':userID2', $this->userID);
                $st->bindParam(':userID3', $this->userID);
				$st->bindParam(':companyID', $this->companyID);	 

				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$projectStatusArray[] = $row;
						
						$this->results = $projectStatusArray;
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>