<?php


use Core\Components\DB\StaticAccessors\DB;

class LeadCreated {
		
		private $db;
		private $companyID;
		private $sort;
		private $userID;
		private $results;
		
		public function __construct() {

			$this->db = DB::getPdo();
			
			}
			
		public function setStatus($companyID, $sort, $groupBy) {
			$this->companyID = $companyID;
			$this->sort = $sort;
			$this->groupBy = $groupBy;
		}

		public function setUser($userID) {
			$this->userID = $userID;
		}
			
		public function getStatus() {
			
			if (!empty($this->companyID)) {

				$sqlStatement = "SELECT 'LeadCreated' AS statusType, leads.firstName, leads.lastName, leads.businessName, 
                leads.createdAt AS time, leads.leadUUID AS link, leads.address, leads.address2, leads.city, leads.state, leads.zip,
                
                IF(user.userID IS NULL, 0, user.userID) AS 'salesID',
                IF(user.userID IS NULL, 'Unspecified Salesperson', CONCAT(user.userFirstName, ' ', user.userLastName)) AS 'salesperson', user.userActive,
                leads.status
				
				FROM leads
                LEFT JOIN user ON user.userID = leads.assignedToUserID
				
				WHERE 
				
				leads.deletedAt IS NULL AND 
			    leads.status IN (1,2) AND
				 
				leads.companyID = :companyID ORDER BY";

				if ($this->groupBy) {
				    $sqlStatement = $sqlStatement . ' user.userLastName ASC, user.userFirstName ASC,';
                }

                $sqlStatement = $sqlStatement . ' leads.createdAt';

				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';
					
				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':companyID', $this->companyID);	 
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$projectStatusArray[] = $row;
						
						$this->results = $projectStatusArray;
					}
					
				} 
				
			} 
		}

		public function getStatusUser() {
			
			if (!empty($this->companyID) && !empty($this->userID)) {

				$sqlStatement = "SELECT 'LeadCreated' AS statusType, leads.firstName, leads.lastName, leads.businessName, 
                leads.createdAt AS time, leads.leadUUID AS link, leads.address, leads.address2, leads.city, leads.state, leads.zip,
                
                :userID1 AS 'salesID',
                leads.status

                FROM leads
                LEFT JOIN user ON user.userID = leads.assignedToUserID
				
				WHERE 
				
				leads.deletedAt IS NULL AND 
			    leads.status IN (1,2) AND
				      
				leads.assignedToUserID = :userID2 AND 
				 
				leads.companyID = :companyID ORDER BY leads.createdAt";


				if ($this->sort == 'asc') {
					$sqlStatement = $sqlStatement . ' ASC';
					
				} else {
					$sqlStatement = $sqlStatement . ' DESC';
				}
				
				$st = $this->db->prepare($sqlStatement); 
				
				$st->bindParam(':userID1', $this->userID);
                $st->bindParam(':userID2', $this->userID);
				$st->bindParam(':companyID', $this->companyID);	 
				
				$st->execute();
				
				if ($st->rowCount()>=1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$projectStatusArray[] = $row;
						
						$this->results = $projectStatusArray;
					}
					
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
		
	}
	

?>