<?php

use App\NotificationJobs\User\BidRejectedNotificationJob;
use Ramsey\Uuid\Uuid;

class Bid {
		
		private $db;
		private $evaluationID;
		private $customEvaluation;
		private $results;
		
		
		public function __construct() {
			
			$this->db = new Connection();
			$this->db = $this->db->dbConnect();
			
			}
			
		public function setBid($evaluationID, $customEvaluation) {
			
			$this->evaluationID = $evaluationID;
			$this->customEvaluation = $customEvaluation;
		}

    /**
     * @throws Exception
     */
    public function sendBid() {
			
			if (!empty($this->evaluationID)) {
				
				if (empty($this->customEvaluation)) {

					$st = $this->db->prepare("SELECT evaluationID FROM `evaluationBid` WHERE evaluationID = :evaluationID AND `bidAccepted` IS NOT NULL");
					
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					$st->execute(); 

					if ($st->rowCount()>=1) {
						
						
					} else {
						$st = $this->db->prepare("
							UPDATE evaluationBid SET	
							bidRejected = UTC_TIMESTAMP
							WHERE evaluationID = :evaluationID"
					
						);
						
						$st->bindParam(':evaluationID', $this->evaluationID);
						
						$st->execute(); 
					
						$stTwo = $this->db->prepare("
							SELECT `bidRejected` FROM `evaluationBid` WHERE evaluationID = :evaluationID"
					
						);
						
						$stTwo->bindParam(':evaluationID', $this->evaluationID);
						
						$stTwo->execute(); 
						
						if ($stTwo->rowCount()>=1) {
							while ($row = $stTwo->fetch((PDO::FETCH_ASSOC))) {
								$returnBid = $row;
							
							}
							$this->results = $returnBid; 
						} 
					}
					
				} else {
				
				$st = $this->db->prepare("
						UPDATE customBid SET	
						bidRejected = UTC_TIMESTAMP
						WHERE evaluationID = :evaluationID"
				
					);
					
					$st->bindParam(':evaluationID', $this->evaluationID);
					
					$st->execute(); 
				
					$stTwo = $this->db->prepare("
						SELECT `bidRejected`, e.evaluationFinalizedByID, p.projectSalesperson
						
						FROM `customBid` 
						JOIN evaluation AS e ON e.evaluationID = customBid.evaluationID
                        JOIN project AS p ON p.projectID = e.projectID
						
						WHERE customBid.evaluationID = :evaluationID"
				
					);
					
					$stTwo->bindParam(':evaluationID', $this->evaluationID);
					
					$stTwo->execute(); 
					
					if ($stTwo->rowCount()>=1) {
						while ($row = $stTwo->fetch((PDO::FETCH_ASSOC))) {
							$returnBid = $row;
						
						}
						$this->results = $returnBid; 
					} 
				}

                BidRejectedNotificationJob::enqueue((int) $this->evaluationID);
			}
		}

		public function getResults () {
		 	return $this->results;
		}
	}
	

?>