<?php


use Core\Components\DB\StaticAccessors\DB;

class Property {
		
		private $db;
		private $propertyID;
		private $companyID;
		private $results;
		
		public function __construct() {
			
			$this->db = DB::getPdo();
			
			}
			
		public function setProperty($propertyID, $companyID) {
			$this->propertyID = $propertyID;
			$this->companyID = $companyID;
		}
			
			
		public function getProperty() {
			
			if (!empty($this->propertyID) && !empty($this->companyID)) {
				
				$st = $this->db->prepare(("SELECT 
					m.customerID, p.propertyID, m.firstName, m.lastName, p.address, p.address2, p.city, p.state, p.zip, p.county, p.township, p.latitude, p.longitude, m.ownerAddress, m.ownerAddress2, m.ownerCity, m.ownerState, 
					m.ownerZip, m.email, m.businessName
				FROM property AS p 
				
            	LEFT JOIN customer AS m ON m.customerID = p.customerID
            	LEFT JOIN companies AS c ON c.companyID = m.companyID 
	
				WHERE p.propertyID=? AND p.deletedAt IS NULL AND c.companyID=? LIMIT 1"));
				//write parameter query to avoid sql injections
				$st->bindParam(1, $this->propertyID);
				$st->bindParam(2, $this->companyID);
				
				$st->execute();
				
				if ($st->rowCount()==1) {
					while ($row = $st->fetch((PDO::FETCH_ASSOC))) {
						$returnProperty = $row;
						
					}
					
					$this->results = $returnProperty; 
				} 
				
			} 
		}
		
		public function getResults () {
		 	return $this->results;
		}
		
	}
	

?>