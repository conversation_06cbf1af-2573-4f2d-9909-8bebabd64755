<?php

use App\Services\Email\Types\Customer\LegacyBidRejectedType;

if(isset($_GET['id'])) {
		$bidID = filter_input(INPUT_GET, 'id', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if(isset($_GET['resend'])) {
		$resendEmail = filter_input(INPUT_GET, 'resend', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	} else {
		$resendEmail = NULL;
	}

	include_once(__DIR__ . '/includes/classes/class_FindEvaluation.php');
		
		$object = new Bid();
		$object->setBid($bidID);
		$object->getEvaluation();
		$bidArray = $object->getResults();	
	
		//Find Evaluation
		$evaluationID = $bidArray['evaluationID'];
		$companyID = $bidArray['companyID'];
		$customEvaluation = $bidArray['customEvaluation'];
		$bidItemID = $bidArray['bidItemID'];
	
		if (empty($evaluationID)) {
			echo 'Bid not found!';
			return;
		}

		if (($customer = \Common\Models\Customer::find($bidArray['customerID'])) === null) {
		    return;
        }
		if ($customer->canEmail()) {
            LegacyBidRejectedType::send([
                'evaluation_id' => $evaluationID
            ]);
        }
	
		if ($resendEmail == 'resend') {
			echo json_encode('true');
		} else {
            return Response::redirect()->to('view-bid.php', function ($uri) use ($bidID) {
                $uri->query(['id' => $bidID]);
            });
		}

	

?>