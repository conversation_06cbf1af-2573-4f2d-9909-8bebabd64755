<?php

    use App\Classes\FX\API\GetDrawingsForEvaluation;

	header('Content-Type: application/json');
	
	if (isset($_POST["token"])) {
		$token = filter_input(INPUT_POST, 'token', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["evaluationID"])) {
		$evaluationID = filter_input(INPUT_POST, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
	}

	$object = new GetDrawingsForEvaluation();
	$object->setToken($token);
	$object->setEvaluationID($evaluationID);;
	$object->authenticate();
	$response = $object->getResults();

	echo json_encode($response);
?>