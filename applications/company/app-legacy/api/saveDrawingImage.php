<?php

    use App\Classes\FX\API\SaveDrawingImage;

	header('Content-Type: application/json');
	
	if (isset($_POST["token"])) {
		$token = filter_input(INPUT_POST, 'token', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_POST["drawingID"])) {
		$UUID = filter_input(INPUT_POST, 'drawingID', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (isset($_FILES)) {
		$fileName = $_FILES['file']['name'];
		$fileType = $_FILES['file']['type'];
		$fileContent = $_FILES['file']['tmp_name'];
		$fileSize = $_FILES['file']['size']; 
		$object = new SaveDrawingImage();
		$object->setToken($token);
		$object->setUUID($UUID);
		$object->setFile($fileName, $fileContent, $fileType, $fileSize);
		$object->save();
		$response = $object->getResults();
		echo json_encode($response);
	}