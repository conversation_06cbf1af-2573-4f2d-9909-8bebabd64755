<?php

use App\Classes\Func;
use App\Classes\Template;
use App\Resources\CompanyResource;
use App\Services\CompanySettingService;
use App\Services\TimeService;
use Carbon\Carbon;
use Common\Models\EmailTemplate;
use Common\Models\Evaluation as EvaluationModel;
use Common\Models\EvaluationWarranty as EvaluationWarrantyModel;
use Core\Classes\File;
use Core\Exceptions\AppException;
use Core\StaticAccessors\Path;
use Dompdf\Dompdf;
use Dompdf\Options;
use mikehaertl\pdftk\Pdf;
use Ramsey\Uuid\Uuid;

set_time_limit(120);

	if(isset($_SESSION["userID"])) {
		$userID = $_SESSION['userID'];
	}

	$sendEmail = NULL;
	$companyPhoneDisplayEmail = NULL;
	$companyPhoneDisplayLetter = NULL;
	$warrantyDocument = NULL;
	$warranty = NULL;
	$customEvaluation = NULL;

	$logo = null;
    $logoUrl = null;
    $logoHtml = null;

	if(isset($_GET['projectID'])) {
		$projectID = filter_input(INPUT_GET, 'projectID', FILTER_SANITIZE_NUMBER_INT);
	}

	if(isset($_GET['evaluationID'])) {
		$evaluationID = filter_input(INPUT_GET, 'evaluationID', FILTER_SANITIZE_NUMBER_INT);
	}
	
	if(isset($_GET['email'])) {
		$sendEmail = filter_input(INPUT_GET, 'email', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if(isset($_GET['custom'])) {
		$customEvaluation = filter_input(INPUT_GET, 'custom', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
	}

	if (!isset($_GET['warranties']) || !is_array(($warranties = $_GET['warranties']))) {
	    $warranties = [];
    }

	include_once(__DIR__ . '/includes/classes/class_User.php');
			
		$object = new User();
		$object->setUser($userID);
		$object->getUser();
		$userArray = $object->getResults();	
		
		$userID = $userArray['userID'];
		$companyID = $userArray['companyID'];
		$userFirstName = $userArray['userFirstName'];
		$userLastName = $userArray['userLastName'];
		$userPhoneDirect = $userArray['userPhoneDirect'];
		$userPhoneCell = $userArray['userPhoneCell'];
		$userEmail = $userArray['userEmail'];
		$primary = $userArray['primary'];
		$projectManagement = $userArray['projectManagement'];
		$sales = $userArray['sales'];
		$installation = $userArray['installation'];
		$bidVerification = $userArray['bidVerification'];
		$bidCreation = $userArray['bidCreation'];
		$pierDataRecorder = $userArray['pierDataRecorder'];
		$calendarBgColor = $userArray['calendarBgColor'];

        $setting_service = new CompanySettingService((int) $companyID);
        $email_greeting = $setting_service->get('email_greeting', null);
        $email_greeting =  $email_greeting ?: 'Hello';
		
		if ($primary == 1 || $projectManagement == 1 || $sales == 1) {

            include_once(__DIR__ . '/includes/classes/class_Project.php');

            $object = new Project();
            $object->setProject($projectID, $companyID);
            $object->getProject();
            $projectArray = $object->getResults();

            //Project
            $customerID = $projectArray['customerID'];
            $projectID = $projectArray['projectID'];
            $firstName = $projectArray['firstName'];
            $lastName = $projectArray['lastName'];
            $businessName = $projectArray['businessName'];
            $propertyID = $projectArray['propertyID'];
            $address = $projectArray['address'];
            $address2 = $projectArray['address2'];
            $city = $projectArray['city'];
            $state = $projectArray['state'];
            $zip = $projectArray['zip'];
            $latitude = $projectArray['latitude'];
            $longitude = $projectArray['longitude'];
            $ownerAddress = $projectArray['ownerAddress'];
            $ownerAddress2 = $projectArray['ownerAddress2'];
            $ownerCity = $projectArray['ownerCity'];
            $ownerState = $projectArray['ownerState'];
            $ownerZip = $projectArray['ownerZip'];
            $email = $projectArray['email'];
            $projectDescription = $projectArray['projectDescription'];
            $projectAdded = $projectArray['projectAdded'];
            $projectCancelled = $projectArray['projectCancelled'];
            $projectCancelledByID = $projectArray['projectCancelledByID'];
            $projectCompleted = $projectArray['projectCompleted'];
            $projectCompletedByID = $projectArray['projectCompletedByID'];
            $companyID = $projectArray['companyID'];
            $companyName = $projectArray['name'];
            $companyAddress1 = $projectArray['companyAddress1'];
            $companyAddress2 = $projectArray['companyAddress2'];
            $companyCity = $projectArray['companyCity'];
            $companyState = $projectArray['companyState'];
            $companyZip = $projectArray['companyZip'];
            $companyWebsite = $projectArray['website'];
            $companyColor = $projectArray['color'];
            $companyEmailReply = $projectArray['emailReply'];
            $companyEmailFrom = $projectArray['emailFrom'];
            $cancelledFirstName = $projectArray['cancelledFirstName'];
            $cancelledLastName = $projectArray['cancelledLastName'];
            $cancelledEmail = $projectArray['cancelledEmail'];
            $completedFirstName = $projectArray['completedFirstName'];
            $completedLastName = $projectArray['completedLastName'];
            $completedEmail = $projectArray['completedEmail'];

            if ($address2 == '') {
                $propertyAddress = '
					' . $address . ', ' . $city . ', ' . $state . ' ' . $zip . '';
            } else {
                $propertyAddress = '
					' . $address . ', ' . $address2 . ' ' . $city . ', ' . $state . ' ' . $zip . '';
            }


            if ($ownerAddress2 == '') {
                $customerAddress = '
					' . $ownerAddress . '<br/>' . $ownerCity . ', ' . $ownerState . ' ' . $ownerZip . '';
            } else {
                $customerAddress = '
					' . $ownerAddress . '<br/>' . $ownerAddress2 . ' ' . $ownerCity . ', ' . $ownerState . ' ' . $ownerZip . '';
            }


            $projectCompletedDate = date('F j, Y');

            include_once(__DIR__ . '/includes/classes/class_EvaluationProject.php');

            $object = new EvaluationProject();
            $object->setEvaluation($evaluationID, $companyID, $customEvaluation);
            $object->getEvaluation();
            $evalProjectArray = $object->getResults();

            //Evaluation Project
            $projectID = $evalProjectArray['projectID'];
            $propertyID = $evalProjectArray['propertyID'];
            $customerID = $evalProjectArray['customerID'];
            $firstName = $evalProjectArray['firstName'];
            $lastName = $evalProjectArray['lastName'];
            $businessName = $evalProjectArray['businessName'];
            $address = $evalProjectArray['address'];
            $address2 = $evalProjectArray['address2'];
            $city = $evalProjectArray['city'];
            $state = $evalProjectArray['state'];
            $zip = $evalProjectArray['zip'];
            $ownerAddress = $evalProjectArray['ownerAddress'];
            $ownerAddress2 = $evalProjectArray['ownerAddress2'];
            $ownerCity = $evalProjectArray['ownerCity'];
            $ownerState = $evalProjectArray['ownerState'];
            $ownerZip = $evalProjectArray['ownerZip'];
            $email = $evalProjectArray['email'];
            $projectDescription = $evalProjectArray['projectDescription'];
            $evaluationCreated = $evalProjectArray['evaluationCreated'];
            $createdFirstName = $evalProjectArray['createdFirstName'];
            $createdLastName = $evalProjectArray['createdLastName'];
            $createdEmail = $evalProjectArray['createdEmail'];
            $createdPhone = $evalProjectArray['createdPhone'];
            $bidAccepted = $evalProjectArray['bidAccepted'];
            $bidAcceptedName = $evalProjectArray['bidAcceptedName'];
            $bidRejected = $evalProjectArray['bidRejected'];
            $evaluationCancelled = $evalProjectArray['evaluationCancelled'];
            $contractID = $evalProjectArray['contractID'];
            $evaluationDescription = $evalProjectArray['evaluationDescription'];
            $finalReportSent = $evalProjectArray['finalReportSent'];
            $projectSalesperson = $evalProjectArray['projectSalesperson'];

            /** @var TimeService $time_service */
            $time_service = App::get(TimeService::class);
            $finalReportDate = $finalReportSent ? $time_service->getFromUtc($finalReportSent) : $time_service->get(Carbon::now('UTC'));
            $finalReportDate = $finalReportDate->format('F j, Y');

            include_once(__DIR__ . '/includes/classes/class_Company.php');

            $object = new Company();
            $object->setCompany($companyID);
            $object->getCompany();
            $companyArray = $object->getResults();

            //Company
            $companyID = $companyArray['companyID'];
            $companyName = $companyArray['name'];
            $companyAddress1 = $companyArray['address'];
            $companyAddress2 = $companyArray['address2'];
            $companyCity = $companyArray['city'];
            $companyState = $companyArray['state'];
            $companyZip = $companyArray['zip'];
            $companyWebsite = $companyArray['website'];
            $logoFileID = $companyArray['logoFileID'];
            $companyEmailReply = $companyArray['emailReply'];
            $companyEmailFrom = $companyArray['emailFrom'];
            $timezone = $companyArray['timezone'];
            $daylightSavings = $companyArray['daylightSavings'];
            $companyColor = $companyArray['color'];
            $companyColorHover = $companyArray['colorHover'];
            $companyEmailFinalPacket = $companyArray['emailFinalPacket'];
            $companyCoverLetter = $companyArray['coverLetter'];

            $email_template_data = EmailTemplate::where('ownerID', $companyID)
                ->where('type', EmailTemplate::TYPE_WARRANTIES)
                ->first();

            $companyCoverLetter = htmlspecialchars_decode($companyCoverLetter);

            $companyEmailFinalPacketPlain = strip_tags($email_template_data->content);

            $companyEmailFinalPacket = htmlspecialchars_decode($email_template_data->content);

            if (!empty($logoFileID)) {
                $companyResource = CompanyResource::make(Auth::acl());
                $logoUrl = $companyResource->getMedia()->get('logo')->getVariant('email_thumbnail')->getUrl($companyID)->csm()->build();
                $logo = '<img src="' . $logoUrl . '"/>';
                $logoHtml = '<p style="text-align:center"><img alt="Company Logo" src="'.$logoUrl.'" /></p>';
            }

            if ($companyAddress2 == '') {
                $companyAddressBlock = '
					' . $companyAddress1 . '<br/>
					' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';
                $companyAddressBlockLetter = '
					' . $companyAddress1 . ', ' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';

            } else {
                $companyAddressBlock = '
					' . $companyAddress1 . ', ' . $companyAddress2 . '<br/>
					' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';

                $companyAddressBlockLetter = '
					' . $companyAddress1 . ', ' . $companyAddress2 . ', ' . $companyCity . ', ' . $companyState . ' ' . $companyZip . '<br/>';
            }


            //Phone
            include_once(__DIR__ . '/includes/classes/class_CompanyPhone.php');

            $object = new CompanyPhone();
            $object->setCompany($companyID);
            $object->getPhone();
            $phoneArray = $object->getResults();

            foreach ($phoneArray as &$row) {
                $phoneNumber = $row['phoneNumber'];
                $phoneDescription = $row['phoneDescription'];

                $companyPhoneDisplayLetter .= '
					' . $phoneDescription . ' ' . $phoneNumber . ' | ';

                $companyPhoneDisplayEmail .= '
					' . $phoneDescription . ' ' . $phoneNumber . '<br/>';
            }
            $companyPhoneDisplayLetter = rtrim($companyPhoneDisplayLetter, ' | ');

            //Additional Emails
            include_once(__DIR__ . '/includes/classes/class_ProjectEmail.php');

            $object = new ProjectEmail();
            $object->setProjectID($projectID);
            $object->getProjectEmails();
            $projectEmails = $object->getResults();

            $pdf = new Pdf();

            //Replace Tags
            $tags = array(
                '{customerFirstName}',
                '{customerLastName}',
                '{customerBusinessName}',
                '{propertyAddress}');

            $variables = array(
                $firstName,
                $lastName,
                $businessName,
                $propertyAddress);

            $companyCoverLetter = str_replace($tags, $variables, $companyCoverLetter);
            $companyCoverLetter = htmlspecialchars_decode($companyCoverLetter);

            //Cover Letter
            $html =
                '<html>
			  	 <style>
			  	 	html {padding:0; margin:0;}
				    body { padding:60px 60px 60px 60px; font-family: sans-serif; }
			    	p {margin-top:0;}
			    	h3, h4 {margin-top:0;margin-bottom:0; }
			    	.footer { position: fixed; left: 0px; bottom: -100px; right: 0px; height: 150px; text-align:center;font-weight:normal;font-size:12px; line-height: 1;}
			  	</style>
			  	' . ($logo !== null ? '<p>' . $logo . '</p><br/><br/>' : '') . '
			  	' . $finalReportDate . '<br/><br/><br/>
			  	' . ($businessName != '' ? $businessName : $firstName . ' ' . $lastName) . '<br/>
			  	' . $customerAddress . '
			  	<br/><br/>
			  	' . $companyCoverLetter . '
			  	<p class="footer">
			  		<strong>' . $companyName . '</strong> | ' . $companyAddressBlockLetter . '<br/>
			  		' . $companyPhoneDisplayLetter . '
			  	</p>
			  </html>';
            $dompdf = new Dompdf([
                'isRemoteEnabled' => true,
                'chroot' => realpath(Path::get('company', $companyID))
            ]);
            $dompdf->loadHtml($html);
            $dompdf->render();

            $file_cover = Func::createTempFile('pdf');
            if ($file_cover->fwrite($dompdf->output()) === 0) {
                throw new AppException('No data written to temp cover file: %s', $file_cover->getPathname());
            }
            unset($dompdf);
            $pdf->addFile($file_cover->getPathname());

            if ($sendEmail == 'send') {
                $evaluationModel =  EvaluationModel::find($evaluationID);
                $evaluationWarranties = [];
                if ($evaluationModel !== null) {
                    $evaluationWarranties = $evaluationModel->warranties->pluck('warrantyID')->all();
                }

                $create = array_diff($warranties, $evaluationWarranties);
                $delete = array_diff($evaluationWarranties, $warranties);

                if (count($delete) > 0) {
                    EvaluationWarrantyModel::where('evaluationID', $evaluationID)
                        ->whereIn('warrantyID', $delete)->delete();
                }

                if (count($create) > 0) {
                    $i = 0;
                    foreach ($create as $warranty_id) {
                        EvaluationWarrantyModel::create([
                            'evaluationID' => $evaluationID,
                            'warrantyID' => $warranty_id,
                            'sort' => $i
                        ]);
                        $i++;
                    }
                }
            }

            //Warranty
            include_once(__DIR__ . '/includes/classes/class_EvaluationWarranty.php');

            $object = new EvaluationWarranty();
            $object->setEvaluation($companyID, $evaluationID);
            $object->getEvaluation();
            $warrantyArray = $object->getResults();

			$file_warranties = [];
			if (!empty($warrantyArray)) {

				foreach($warrantyArray as $row) {

					$evaluationID = $row['evaluationID'];
					$warrantyID = $row['warrantyID'];
					$warrantyName = $row['warrantyName'];
					$warrantyText = $row['warrantyText'];
					$warrantyType = $row['warrantyType'];


					//Replace Tags
					$tags = array(
						'{companyAddress}',
						'{companyPhoneNumbers}',
						'{companyLogo}',
						'{customerFirstName}',
						'{customerLastName}',
						'{customerBusinessName}',
						'{customerAddress}',
						'{propertyAddress}',
						'{evaluationName}',
						'{todaysDate}');

					$variables = array(
						$companyAddressBlock,
						$companyPhoneDisplayEmail,
						$logo,
						$firstName,
						$lastName,
						$businessName,
						$customerAddress,
						$propertyAddress,
						$evaluationDescription,
                        $finalReportDate);

						$warrantyText = str_replace($tags, $variables, $warrantyText);

						$warrantyText = htmlspecialchars_decode($warrantyText);

                        $pageOrientation = [];

					if ($warrantyType == 0) {
						$pageOrientation['dompdf'] = 'landscape';
						$pagePadding = '20px 20px 20px 20px';

						$warrantyDocument = '
							<body style="border:20px solid '.$companyColor.';margin:20px 20px -20px 20px;">
								<div>
									'.$warrantyText.'
								</div>
							</body>
						';

					} else if ($warrantyType == 1) {
						$pageOrientation['dompdf'] = 'portrait';
						$pagePadding = '30px 30px 0px 30px';

						$warrantyDocument = '
							<body>
								<div>
									'.$warrantyText.'
								</div>
							</body>
						';
					} else if ($warrantyType == 3) {
                        $pageOrientation['dompdf'] = 'landscape';
                        $pagePadding = '20px 20px 20px 20px';

                        $warrantyDocument = '
							<body>
								<div>
									'.$warrantyText.'
								</div>
							</body>
						';
                    }

					$html =
					  '<html>
					  	 <style>
					  	 	html {padding:0; margin:0;}
						    body { padding:'.$pagePadding.'; font-family: sans-serif; }
					    	p {margin-top:0; }
					    	h3, h4 {margin-top:0;margin-bottom:0; }
					  	</style>
					  	'.$warrantyDocument.'
					  </html>';

                    $dompdf = new Dompdf([
                        'isRemoteEnabled' => true,
                        'chroot' => realpath(Path::get('company', $companyID))
                    ]);
					$dompdf->load_html($html);
					$dompdf->set_paper('letter', $pageOrientation['dompdf']);
					$dompdf->render();

                    $file_warranty = Func::createTempFile('pdf');
                    if ($file_warranty->fwrite($dompdf->output()) === 0) {
                        throw new AppException('No data written to temp warranty file: %s', $file_warranty->getPathname());
                    }
                    unset($dompdf);
                    $pdf->addFile($file_warranty->getPathname());

                    $file_warranties[] = $file_warranty;
				}

			}

			// output merged pdf's into file
            $file_full_report = Func::createTempFile('pdf');
            if (!$pdf->saveAs($file_full_report->getPathname())) {
                throw new AppException('Unable to save merged PDF: %s - Reason: %s', $file_full_report->getPathname(), $pdf->getError());
            }

            $output_name = File::sanitizeName("{$firstName}_{$lastName}_Final_Report", 'pdf');

			if ($sendEmail == 'send' || $sendEmail == 'resend') {

                $propertyAddress = $address;
                $propertyAddress2 = Func::emptyToNull($address2);
                $propertyAddress .= $propertyAddress2 !== null ? ', ' . $propertyAddress2 : '';

                $website_display = '';
                if ($companyWebsite !== null) {
                    $website_display = '<a href="http://'.$companyWebsite.'">'.$companyWebsite.'</a>';
                }

				$body = "
					<html>
						<style type=\"text/css\">
							body {
								height:100% !important;
								background-color:#ffffff;
								margin:0;
								color:#151719;
								font-family: \"Helvetica Neue\", Helvetica, Roboto, Arial, sans-serif;
								line-height:1.5;
							}
							div.emailContent {
								width:100%;
								margin:0px auto 0 auto;
								padding:10px 0 15px 0;
								background-color:#ffffff;
							}
							div.inner {
								margin:0px 30px 0px 30px;
							}
							div.emailFooter {
								width:100%;
							}

							span.highlight {
								color:".$companyColor.";
								font-weight:bold;
							}

							a {
								color:".$companyColor.";
							}

							a:visted {
								color:".$companyColor.";
							}
						</style>
						<body>
							<div class=\"emailContent\">
								<div class=\"inner\">
									<p>
										".$email_greeting." ".$firstName.",
									</p>
									".$companyEmailFinalPacket."
					                ".$logoHtml."
					           	</div>
				          	</div>
				          	<div class=\"emailFooter\">
				          		<div class=\"inner\">
					          		<p style=\"text-align:center;padding-bottom:20px;\">
				               			<span class=\"highlight\">".$companyName."</span> | ".$companyAddress1.", ".$companyCity.", ".$companyState." ".$companyZip."<br/>
				               			".$companyPhoneDisplayEmail."
				               			".$website_display."
				              	 	</p>
				              	</div>
				          	</div>
						</body>
					</html>
					";

				$altbody = $companyEmailFinalPacketPlain;

                $subject = Template::replace($email_template_data->subject, [
                    'company_name' => $companyName,
                    'address' => $propertyAddress
                ]);

				$mail = FXEmail::type(FXEmail::TYPE_CUSTOMER)
					->customerID($customerID)
					->projectID($projectID);
				if ($mail->canSend()) {
                    if ($email_template_data->isSendFromSalesperson && !empty($projectSalesperson)) {
                        $mail->salespersonID($projectSalesperson);
                    }
					$mail->subject($subject);
					$mail->attach($file_full_report->getPathname(), $output_name);
					$mail->html($body, $altbody);
					$mail->send();
				}

				echo json_encode('true');

			} else {
				header('Content-type: application/pdf');
				header('Content-Disposition: inline; filename="' . $output_name . '"');
				header('Content-Transfer-Encoding: binary');
				header('Accept-Ranges: bytes');
				@readfile($file_full_report->getPathname());
			}

			// clean up temp files
			// @todo add better exception handling
			if (!@unlink($file_cover->getPathname())) {
				throw new AppException('Unable to delete temp cover file: %s', $file_cover->getPathname());
			}
			if (count($file_warranties) > 0) {
				foreach ($file_warranties as $file_warranty) {
					if (@unlink($file_warranty->getPathname())) {
						continue;
					}
					throw new AppException('Unable to delete temp warranty file: %s', $file_warranty->getPathname());
				}
			}
			if (!@unlink($file_full_report->getPathname())) {
				throw new AppException('Unable to delete temp full report file: %s', $file_full_report->getPathname());
			}
			exit;
		}
?>